package com.alpha.gallery.feature.mediaviewer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.feature.mediaviewer.util.MediaLoadingUtils
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiAction
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiEvent
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Media Viewer screen
 */
@HiltViewModel
class MediaViewerViewModel @Inject constructor(
    private val mediaRepository: MediaRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MediaViewerUiState())
    val uiState: StateFlow<MediaViewerUiState> = _uiState.asStateFlow()
    
    private val _uiActions = MutableSharedFlow<MediaViewerUiAction>()
    val uiActions: SharedFlow<MediaViewerUiAction> = _uiActions.asSharedFlow()
    
    private var controlsHideJob: kotlinx.coroutines.Job? = null
    
    /**
     * Initialize the media viewer with a specific media item
     */
    fun initialize(mediaItemId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // Load all media items
                val allMediaItems = mediaRepository.getAllMediaItems().first()
                
                // Find the current item and its index
                val currentIndex = allMediaItems.indexOfFirst { it.id == mediaItemId }
                if (currentIndex == -1) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "Media item not found"
                    )
                    return@launch
                }
                
                val currentItem = allMediaItems[currentIndex]
                
                _uiState.value = _uiState.value.copy(
                    currentMediaItem = currentItem,
                    mediaItems = allMediaItems,
                    currentIndex = currentIndex,
                    isLoading = false,
                    hasPermissions = true,
                    showControls = true
                )
                
                // Auto-hide controls after 3 seconds
                scheduleControlsHide()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to load media"
                )
            }
        }
    }
    
    /**
     * Handle UI events
     */
    fun onEvent(event: MediaViewerUiEvent) {
        when (event) {
            is MediaViewerUiEvent.NavigatePrevious -> navigatePrevious()
            is MediaViewerUiEvent.NavigateNext -> navigateNext()
            is MediaViewerUiEvent.NavigateToIndex -> navigateToIndex(event.index)
            is MediaViewerUiEvent.NavigateBack -> navigateBack()
            
            is MediaViewerUiEvent.TogglePlayPause -> togglePlayPause()
            is MediaViewerUiEvent.SeekTo -> seekTo(event.position)
            is MediaViewerUiEvent.RestartVideo -> restartVideo()
            
            is MediaViewerUiEvent.ToggleControls -> toggleControls()
            is MediaViewerUiEvent.ToggleMetadata -> toggleMetadata()
            is MediaViewerUiEvent.ToggleFullscreen -> toggleFullscreen()
            is MediaViewerUiEvent.HideControls -> hideControls()
            is MediaViewerUiEvent.ShowControls -> showControls()
            
            is MediaViewerUiEvent.ZoomTo -> zoomTo(event.scale)
            is MediaViewerUiEvent.ResetZoom -> resetZoom()
            
            is MediaViewerUiEvent.ToggleFavorite -> toggleFavorite()
            is MediaViewerUiEvent.ShareMedia -> shareMedia()
            is MediaViewerUiEvent.DeleteMedia -> deleteMedia()
            
            is MediaViewerUiEvent.RetryLoad -> retryLoad()
            is MediaViewerUiEvent.ClearError -> clearError()
            
            is MediaViewerUiEvent.OnResume -> onResume()
            is MediaViewerUiEvent.OnPause -> onPause()
            is MediaViewerUiEvent.OnStop -> onStop()
        }
    }
    
    private fun navigatePrevious() {
        val currentState = _uiState.value
        if (currentState.canNavigatePrevious) {
            val newIndex = currentState.currentIndex - 1
            val newItem = currentState.mediaItems[newIndex]
            
            _uiState.value = currentState.copy(
                currentMediaItem = newItem,
                currentIndex = newIndex,
                isPlaying = false,
                playbackPosition = 0L,
                zoomScale = 1f
            )
            
            showControlsTemporarily()
        }
    }
    
    private fun navigateNext() {
        val currentState = _uiState.value
        if (currentState.canNavigateNext) {
            val newIndex = currentState.currentIndex + 1
            val newItem = currentState.mediaItems[newIndex]
            
            _uiState.value = currentState.copy(
                currentMediaItem = newItem,
                currentIndex = newIndex,
                isPlaying = false,
                playbackPosition = 0L,
                zoomScale = 1f
            )
            
            showControlsTemporarily()
        }
    }
    
    private fun navigateToIndex(index: Int) {
        val currentState = _uiState.value
        if (index in 0 until currentState.mediaItems.size) {
            val newItem = currentState.mediaItems[index]
            
            _uiState.value = currentState.copy(
                currentMediaItem = newItem,
                currentIndex = index,
                isPlaying = false,
                playbackPosition = 0L,
                zoomScale = 1f
            )
            
            showControlsTemporarily()
        }
    }
    
    private fun navigateBack() {
        _uiActions.tryEmit(MediaViewerUiAction.NavigateBack)
    }
    
    private fun togglePlayPause() {
        _uiState.value = _uiState.value.copy(
            isPlaying = !_uiState.value.isPlaying
        )
        showControlsTemporarily()
    }
    
    private fun seekTo(position: Long) {
        _uiState.value = _uiState.value.copy(
            playbackPosition = position
        )
        showControlsTemporarily()
    }
    
    private fun restartVideo() {
        _uiState.value = _uiState.value.copy(
            playbackPosition = 0L,
            isPlaying = true
        )
        showControlsTemporarily()
    }
    
    private fun toggleControls() {
        val showControls = !_uiState.value.showControls
        _uiState.value = _uiState.value.copy(showControls = showControls)
        
        if (showControls) {
            scheduleControlsHide()
        } else {
            cancelControlsHide()
        }
    }
    
    private fun toggleMetadata() {
        _uiState.value = _uiState.value.copy(
            showMetadata = !_uiState.value.showMetadata
        )
        showControlsTemporarily()
    }
    
    private fun toggleFullscreen() {
        val isFullscreen = !_uiState.value.isFullscreen
        _uiState.value = _uiState.value.copy(isFullscreen = isFullscreen)
        
        if (isFullscreen) {
            _uiActions.tryEmit(MediaViewerUiAction.HideSystemUI)
        } else {
            _uiActions.tryEmit(MediaViewerUiAction.ShowSystemUI)
        }
        
        showControlsTemporarily()
    }
    
    private fun hideControls() {
        _uiState.value = _uiState.value.copy(showControls = false)
        cancelControlsHide()
    }
    
    private fun showControls() {
        _uiState.value = _uiState.value.copy(showControls = true)
        scheduleControlsHide()
    }
    
    private fun showControlsTemporarily() {
        _uiState.value = _uiState.value.copy(showControls = true)
        scheduleControlsHide()
    }
    
    private fun scheduleControlsHide() {
        cancelControlsHide()
        controlsHideJob = viewModelScope.launch {
            kotlinx.coroutines.delay(3000) // Hide after 3 seconds
            _uiState.value = _uiState.value.copy(showControls = false)
        }
    }
    
    private fun cancelControlsHide() {
        controlsHideJob?.cancel()
        controlsHideJob = null
    }
    
    private fun zoomTo(scale: Float) {
        _uiState.value = _uiState.value.copy(
            zoomScale = scale.coerceIn(0.5f, 5f)
        )
    }
    
    private fun resetZoom() {
        _uiState.value = _uiState.value.copy(zoomScale = 1f)
    }
    
    private fun toggleFavorite() {
        val currentItem = _uiState.value.currentMediaItem ?: return
        
        viewModelScope.launch {
            try {
                if (currentItem.isFavorite) {
                    mediaRepository.removeFromFavorites(currentItem.id)
                    _uiActions.tryEmit(MediaViewerUiAction.ShowMessage("Removed from favorites"))
                } else {
                    mediaRepository.addToFavorites(currentItem.id)
                    _uiActions.tryEmit(MediaViewerUiAction.ShowMessage("Added to favorites"))
                }
                
                // Update the current item in state
                val updatedItem = currentItem.copy(isFavorite = !currentItem.isFavorite)
                val updatedItems = _uiState.value.mediaItems.toMutableList()
                updatedItems[_uiState.value.currentIndex] = updatedItem
                
                _uiState.value = _uiState.value.copy(
                    currentMediaItem = updatedItem,
                    mediaItems = updatedItems
                )
                
            } catch (e: Exception) {
                _uiActions.tryEmit(MediaViewerUiAction.ShowError("Failed to update favorites"))
            }
        }
    }
    
    private fun shareMedia() {
        val currentItem = _uiState.value.currentMediaItem ?: return
        _uiActions.tryEmit(
            MediaViewerUiAction.ShareMedia(
                mediaUri = currentItem.uri,
                mimeType = currentItem.mimeType
            )
        )
    }
    
    private fun deleteMedia() {
        val currentItem = _uiState.value.currentMediaItem ?: return
        
        viewModelScope.launch {
            try {
                val success = mediaRepository.deleteMediaItem(currentItem.id)
                if (success) {
                    _uiActions.tryEmit(MediaViewerUiAction.ShowMessage("Media deleted"))
                    
                    // Navigate to next item or go back if this was the last item
                    val currentState = _uiState.value
                    val updatedItems = currentState.mediaItems.toMutableList()
                    updatedItems.removeAt(currentState.currentIndex)
                    
                    if (updatedItems.isEmpty()) {
                        // No more items, go back
                        navigateBack()
                    } else {
                        // Navigate to next item or previous if we were at the end
                        val newIndex = if (currentState.currentIndex >= updatedItems.size) {
                            updatedItems.size - 1
                        } else {
                            currentState.currentIndex
                        }
                        
                        _uiState.value = currentState.copy(
                            currentMediaItem = updatedItems[newIndex],
                            mediaItems = updatedItems,
                            currentIndex = newIndex,
                            isPlaying = false,
                            playbackPosition = 0L,
                            zoomScale = 1f
                        )
                    }
                } else {
                    _uiActions.tryEmit(MediaViewerUiAction.ShowError("Failed to delete media"))
                }
            } catch (e: Exception) {
                _uiActions.tryEmit(MediaViewerUiAction.ShowError("Failed to delete media"))
            }
        }
    }
    
    private fun retryLoad() {
        val currentItem = _uiState.value.currentMediaItem
        if (currentItem != null) {
            initialize(currentItem.id)
        }
    }
    
    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    private fun onResume() {
        // Resume video playback if it was playing
        if (_uiState.value.isVideo && _uiState.value.isPlaying) {
            // Video player will handle resume
        }
    }
    
    private fun onPause() {
        // Pause video playback
        if (_uiState.value.isVideo && _uiState.value.isPlaying) {
            _uiState.value = _uiState.value.copy(isPlaying = false)
        }
        cancelControlsHide()
    }
    
    private fun onStop() {
        // Stop video playback and reset position
        if (_uiState.value.isVideo) {
            _uiState.value = _uiState.value.copy(
                isPlaying = false,
                playbackPosition = 0L
            )
        }
        cancelControlsHide()
    }
    
    /**
     * Update playback state from video player
     */
    fun updatePlaybackState(
        isPlaying: Boolean,
        position: Long,
        duration: Long,
        isBuffering: Boolean = false
    ) {
        _uiState.value = _uiState.value.copy(
            isPlaying = isPlaying,
            playbackPosition = position,
            playbackDuration = duration,
            isBuffering = isBuffering
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        cancelControlsHide()
    }
}
