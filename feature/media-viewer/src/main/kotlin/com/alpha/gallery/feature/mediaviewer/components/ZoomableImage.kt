package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.DecayAnimationSpec
import androidx.compose.animation.core.exponentialDecay
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.calculateCentroid
import androidx.compose.foundation.gestures.calculateCentroidSize
import androidx.compose.foundation.gestures.calculatePan
import androidx.compose.foundation.gestures.calculateRotation
import androidx.compose.foundation.gestures.calculateZoom
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.feature.mediaviewer.util.MediaLoadingUtils
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * Advanced zoomable image component with smooth animations and gesture handling
 */
@Composable
fun ZoomableImage(
    mediaItem: MediaItem,
    onTap: () -> Unit = {},
    onZoomChange: (Float) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // Animation states
    val scale = remember { Animatable(1f) }
    val offsetX = remember { Animatable(0f) }
    val offsetY = remember { Animatable(0f) }
    
    // Loading and error states
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    
    // Gesture state
    var isTransforming by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        AsyncImage(
            model = MediaLoadingUtils.createImageRequest(context, mediaItem),
            contentDescription = mediaItem.name,
            contentScale = ContentScale.Fit,
            onState = { state ->
                isLoading = state is AsyncImagePainter.State.Loading
                hasError = state is AsyncImagePainter.State.Error
            },
            modifier = Modifier
                .fillMaxSize()
                .clip(RectangleShape)
                .graphicsLayer(
                    scaleX = scale.value,
                    scaleY = scale.value,
                    translationX = offsetX.value,
                    translationY = offsetY.value
                )
                .pointerInput(Unit) {
                    detectTapGestures(
                        onDoubleTap = { tapOffset ->
                            coroutineScope.launch {
                                if (scale.value > 1f) {
                                    // Zoom out to fit
                                    scale.animateTo(1f)
                                    offsetX.animateTo(0f)
                                    offsetY.animateTo(0f)
                                    onZoomChange(1f)
                                } else {
                                    // Zoom in to 2x at tap location
                                    val targetScale = 2f
                                    scale.animateTo(targetScale)
                                    
                                    // Calculate offset to center on tap location
                                    val centerX = size.width / 2f
                                    val centerY = size.height / 2f
                                    val offsetXTarget = (centerX - tapOffset.x) * (targetScale - 1f)
                                    val offsetYTarget = (centerY - tapOffset.y) * (targetScale - 1f)
                                    
                                    offsetX.animateTo(offsetXTarget)
                                    offsetY.animateTo(offsetYTarget)
                                    onZoomChange(targetScale)
                                }
                            }
                        },
                        onTap = { onTap() }
                    )
                }
                .pointerInput(Unit) {
                    awaitEachGesture {
                        awaitFirstDown()
                        do {
                            val event = awaitPointerEvent()
                            val canceled = event.changes.any { it.isConsumed }
                            if (!canceled) {
                                val zoomChange = event.calculateZoom()
                                val panChange = event.calculatePan()

                                if (!isTransforming) {
                                    isTransforming = true
                                }

                                if (abs(zoomChange - 1f) > 0.02f) {
                                    // Handle zoom
                                    val newScale = (scale.value * zoomChange).coerceIn(0.5f, 5f)
                                    coroutineScope.launch {
                                        scale.snapTo(newScale)
                                        onZoomChange(newScale)
                                    }
                                    event.changes.forEach { it.consume() }
                                }

                                if (scale.value > 1f && (abs(panChange.x) > 1f || abs(panChange.y) > 1f)) {
                                    // Handle pan (only when zoomed in)
                                    coroutineScope.launch {
                                        offsetX.snapTo(offsetX.value + panChange.x)
                                        offsetY.snapTo(offsetY.value + panChange.y)
                                    }
                                    event.changes.forEach { it.consume() }
                                }
                            }
                        } while (event.changes.any { it.pressed })

                        // Gesture ended
                        isTransforming = false

                        // Animate back to bounds if needed
                        coroutineScope.launch {
                            val maxOffsetX = (size.width * (scale.value - 1f)) / 2f
                            val maxOffsetY = (size.height * (scale.value - 1f)) / 2f

                            if (abs(offsetX.value) > maxOffsetX) {
                                offsetX.animateTo(
                                    if (offsetX.value > 0) maxOffsetX else -maxOffsetX
                                )
                            }

                            if (abs(offsetY.value) > maxOffsetY) {
                                offsetY.animateTo(
                                    if (offsetY.value > 0) maxOffsetY else -maxOffsetY
                                )
                            }

                            // Reset position if zoomed out
                            if (scale.value <= 1f) {
                                offsetX.animateTo(0f)
                                offsetY.animateTo(0f)
                            }
                        }
                    }
                }
        )
        
        // Loading indicator
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        // Error state
        if (hasError) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}
