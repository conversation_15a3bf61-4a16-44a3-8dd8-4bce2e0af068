package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.alpha.gallery.core.domain.model.MediaItem
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Metadata overlay for displaying media information
 */
@Composable
fun MediaViewerMetadata(
    mediaItem: MediaItem,
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = true,
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it }),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.9f)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // Header with close button
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Media Information",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.White
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Metadata items
                MetadataItem(
                    label = "Name",
                    value = mediaItem.name
                )
                
                MetadataItem(
                    label = "Type",
                    value = if (mediaItem.isVideo) "Video" else "Image"
                )
                
                MetadataItem(
                    label = "Size",
                    value = formatFileSize(mediaItem.size)
                )
                
                if (mediaItem.width > 0 && mediaItem.height > 0) {
                    MetadataItem(
                        label = "Dimensions",
                        value = "${mediaItem.width} × ${mediaItem.height}"
                    )
                }
                
                if (mediaItem.isVideo && mediaItem.duration > 0) {
                    MetadataItem(
                        label = "Duration",
                        value = formatDuration(mediaItem.duration)
                    )
                }
                
                MetadataItem(
                    label = "Date Added",
                    value = formatDate(mediaItem.dateAdded)
                )
                
                MetadataItem(
                    label = "Date Modified",
                    value = formatDate(mediaItem.dateModified)
                )
                
                mediaItem.albumName?.let { albumName ->
                    if (albumName.isNotBlank()) {
                        MetadataItem(
                            label = "Album",
                            value = albumName
                        )
                    }
                }
                
                MetadataItem(
                    label = "MIME Type",
                    value = mediaItem.mimeType
                )
                
                MetadataItem(
                    label = "Path",
                    value = mediaItem.path,
                    isPath = true
                )
            }
        }
    }
}

/**
 * Individual metadata item
 */
@Composable
private fun MetadataItem(
    label: String,
    value: String,
    isPath: Boolean = false,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Text(
            text = label,
            color = Color.White.copy(alpha = 0.7f),
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(2.dp))
        
        Text(
            text = value,
            color = Color.White,
            fontSize = 14.sp,
            maxLines = if (isPath) 2 else 1
        )
        
        Spacer(modifier = Modifier.height(8.dp))
    }
}

/**
 * Format file size in bytes to human readable format
 */
private fun formatFileSize(bytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = bytes.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return String.format("%.1f %s", size, units[unitIndex])
}

/**
 * Format duration in milliseconds to human readable format
 */
private fun formatDuration(durationMs: Long): String {
    val totalSeconds = durationMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return when {
        hours > 0 -> String.format("%d:%02d:%02d", hours, minutes, seconds)
        else -> String.format("%d:%02d", minutes, seconds)
    }
}

/**
 * Format timestamp to human readable date
 */
private fun formatDate(timestamp: Long): String {
    val date = Date(timestamp * 1000) // Convert to milliseconds
    val formatter = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
    return formatter.format(date)
}
