package com.alpha.gallery.feature.mediaviewer.model

/**
 * UI events for the Media Viewer screen
 */
sealed class MediaViewerUiEvent {
    
    // Navigation events
    data object NavigatePrevious : MediaViewerUiEvent()
    data object NavigateNext : MediaViewerUiEvent()
    data class NavigateToIndex(val index: Int) : MediaViewerUiEvent()
    data object NavigateBack : MediaViewerUiEvent()
    
    // Media control events
    data object TogglePlayPause : MediaViewerUiEvent()
    data class SeekTo(val position: Long) : MediaViewerUiEvent()
    data object RestartVideo : MediaViewerUiEvent()
    
    // UI control events
    data object ToggleControls : MediaViewerUiEvent()
    data object ToggleMetadata : MediaViewerUiEvent()
    data object ToggleFullscreen : MediaViewerUiEvent()
    data object HideControls : MediaViewerUiEvent()
    data object ShowControls : MediaViewerUiEvent()
    
    // Zoom events
    data class ZoomTo(val scale: Float) : MediaViewerUiEvent()
    data object ResetZoom : MediaViewerUiEvent()
    
    // Action events
    data object ToggleFavorite : MediaViewerUiEvent()
    data object ShareMedia : MediaViewerUiEvent()
    data object DeleteMedia : MediaViewerUiEvent()
    
    // Error handling
    data object RetryLoad : MediaViewerUiEvent()
    data object ClearError : MediaViewerUiEvent()
    
    // Lifecycle events
    data object OnResume : MediaViewerUiEvent()
    data object OnPause : MediaViewerUiEvent()
    data object OnStop : MediaViewerUiEvent()
}
