package com.alpha.gallery.feature.mediaviewer.util

import android.content.Context
import android.net.Uri
import coil.request.ImageRequest
import coil.size.Size
import com.alpha.gallery.core.domain.model.MediaItem

/**
 * Utility functions for media loading
 */
object MediaLoadingUtils {
    
    /**
     * Create an optimized image request for the media viewer
     */
    fun createImageRequest(
        context: Context,
        mediaItem: MediaItem,
        size: Size = Size.ORIGINAL
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(Uri.parse(mediaItem.uri))
            .size(size)
            .crossfade(300)
            .memoryCacheKey("media_viewer_${mediaItem.id}")
            .diskCacheKey("media_viewer_${mediaItem.id}")
            .build()
    }
    
    /**
     * Create a thumbnail image request for video preview
     */
    fun createVideoThumbnailRequest(
        context: Context,
        mediaItem: MediaItem
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(Uri.parse(mediaItem.uri))
            .size(Size(400, 400)) // Reasonable thumbnail size
            .crossfade(200)
            .memoryCacheKey("video_thumb_${mediaItem.id}")
            .diskCacheKey("video_thumb_${mediaItem.id}")
            .build()
    }
    
    /**
     * Check if the media item exists and is accessible
     */
    fun isMediaAccessible(context: Context, mediaItem: MediaItem): Boolean {
        return try {
            val uri = Uri.parse(mediaItem.uri)
            context.contentResolver.openInputStream(uri)?.use { true } ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get appropriate error message for media loading failures
     */
    fun getErrorMessage(throwable: Throwable?): String {
        return when {
            throwable?.message?.contains("Permission denied") == true -> 
                "Permission denied. Please grant storage access."
            throwable?.message?.contains("No such file") == true -> 
                "Media file not found. It may have been moved or deleted."
            throwable?.message?.contains("Network") == true -> 
                "Network error. Please check your connection."
            else -> "Failed to load media. Please try again."
        }
    }
}
