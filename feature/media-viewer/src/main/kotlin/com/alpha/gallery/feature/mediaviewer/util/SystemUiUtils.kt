package com.alpha.gallery.feature.mediaviewer.util

import android.app.Activity
import android.view.View
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalContext
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * Utility functions for managing system UI in media viewer
 */
object SystemUiUtils {
    
    /**
     * Hide system UI for fullscreen experience
     */
    fun hideSystemUI(activity: Activity) {
        val window = activity.window
        val decorView = window.decorView
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val controller = WindowInsetsControllerCompat(window, decorView)
        controller.hide(WindowInsetsCompat.Type.systemBars())
        controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        
        // Keep screen on while viewing media
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    /**
     * Show system UI
     */
    fun showSystemUI(activity: Activity) {
        val window = activity.window
        val decorView = window.decorView
        
        WindowCompat.setDecorFitsSystemWindows(window, true)
        
        val controller = WindowInsetsControllerCompat(window, decorView)
        controller.show(WindowInsetsCompat.Type.systemBars())
        
        // Allow screen to turn off normally
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
}

/**
 * Composable for managing system UI state
 */
@Composable
fun SystemUiController(
    isFullscreen: Boolean,
    keepScreenOn: Boolean = true
) {
    val context = LocalContext.current
    
    DisposableEffect(isFullscreen, keepScreenOn) {
        val activity = context as? Activity
        if (activity != null) {
            if (isFullscreen) {
                SystemUiUtils.hideSystemUI(activity)
            } else {
                SystemUiUtils.showSystemUI(activity)
            }
            
            if (keepScreenOn) {
                activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }
        
        onDispose {
            if (activity != null) {
                SystemUiUtils.showSystemUI(activity)
                if (keepScreenOn) {
                    activity.window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
            }
        }
    }
}
