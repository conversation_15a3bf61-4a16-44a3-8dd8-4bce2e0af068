package com.alpha.gallery.feature.mediaviewer.components

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Video player component using ExoPlayer
 */
@Composable
fun VideoPlayer(
    uri: String,
    isPlaying: Boolean = false,
    playbackPosition: Long = 0L,
    onPlaybackUpdate: (Boolean, Long, Long, Boolean) -> Unit = { _, _, _, _ -> },
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var exoPlayer by remember { mutableStateOf<ExoPlayer?>(null) }
    var playerView by remember { mutableStateOf<PlayerView?>(null) }
    var isBuffering by remember { mutableStateOf(false) }
    var duration by remember { mutableLongStateOf(0L) }
    var currentPosition by remember { mutableLongStateOf(0L) }
    
    // Initialize ExoPlayer
    LaunchedEffect(uri) {
        if (exoPlayer == null) {
            exoPlayer = ExoPlayer.Builder(context).build().apply {
                val mediaItem = MediaItem.fromUri(Uri.parse(uri))
                setMediaItem(mediaItem)
                prepare()
                
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        isBuffering = playbackState == Player.STATE_BUFFERING
                        onPlaybackUpdate(
                            isPlaying,
                            currentPosition,
                            duration,
                            isBuffering
                        )
                    }
                    
                    override fun onIsPlayingChanged(playing: Boolean) {
                        onPlaybackUpdate(
                            playing,
                            currentPosition,
                            duration,
                            isBuffering
                        )
                    }
                })
            }
        }
    }
    
    // Handle play/pause state changes
    LaunchedEffect(isPlaying) {
        exoPlayer?.let { player ->
            if (isPlaying) {
                player.play()
            } else {
                player.pause()
            }
        }
    }
    
    // Handle seek position changes
    LaunchedEffect(playbackPosition) {
        exoPlayer?.let { player ->
            if (kotlin.math.abs(player.currentPosition - playbackPosition) > 1000) {
                player.seekTo(playbackPosition)
            }
        }
    }
    
    // Update playback progress
    LaunchedEffect(exoPlayer) {
        while (isActive && exoPlayer != null) {
            exoPlayer?.let { player ->
                currentPosition = player.currentPosition
                duration = player.duration.takeIf { it > 0 } ?: 0L
                
                onPlaybackUpdate(
                    player.isPlaying,
                    currentPosition,
                    duration,
                    isBuffering
                )
            }
            delay(100) // Update every 100ms
        }
    }
    
    // Cleanup
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer?.release()
            exoPlayer = null
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        // ExoPlayer view
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    player = exoPlayer
                    useController = false // We'll use our own controls
                    playerView = this
                }
            },
            update = { view ->
                view.player = exoPlayer
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // Buffering indicator
        if (isBuffering) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}
