package com.alpha.gallery.feature.mediaviewer.model

/**
 * UI actions for the Media Viewer screen
 */
sealed class MediaViewerUiAction {
    
    // Navigation actions
    data object NavigateBack : MediaViewerUiAction()
    
    // Message actions
    data class ShowMessage(val message: String) : MediaViewerUiAction()
    data class ShowError(val error: String) : MediaViewerUiAction()
    
    // Share actions
    data class ShareMedia(val mediaUri: String, val mimeType: String) : MediaViewerUiAction()
    
    // System actions
    data object RequestStoragePermission : MediaViewerUiAction()
    data object HideSystemUI : MediaViewerUiAction()
    data object ShowSystemUI : MediaViewerUiAction()
}
