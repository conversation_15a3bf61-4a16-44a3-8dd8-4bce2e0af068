package com.alpha.gallery.feature.mediaviewer.model

import com.alpha.gallery.core.domain.model.MediaItem

/**
 * UI state for the Media Viewer screen
 */
data class MediaViewerUiState(
    val currentMediaItem: MediaItem? = null,
    val mediaItems: List<MediaItem> = emptyList(),
    val currentIndex: Int = 0,
    val isLoading: Boolean = false,
    val isLoadingMedia: Boolean = false,
    val error: String? = null,
    val showControls: Boolean = true,
    val showMetadata: Boolean = false,
    val isPlaying: Boolean = false,
    val playbackPosition: Long = 0L,
    val playbackDuration: Long = 0L,
    val isBuffering: Boolean = false,
    val zoomScale: Float = 1f,
    val hasPermissions: Boolean = false,
    val isFullscreen: Boolean = false
) {
    val showLoadingState: Boolean
        get() = isLoading && currentMediaItem == null
    
    val showError: Boolean
        get() = error != null && !isLoading
    
    val showContent: <PERSON>ole<PERSON>
        get() = currentMediaItem != null && hasPermissions
    
    val canNavigatePrevious: Boolean
        get() = mediaItems.isNotEmpty() && currentIndex > 0
    
    val canNavigateNext: Boolean
        get() = mediaItems.isNotEmpty() && currentIndex < mediaItems.size - 1
    
    val isVideo: Boolean
        get() = currentMediaItem?.isVideo == true
    
    val isImage: Boolean
        get() = currentMediaItem?.isVideo == false
    
    val selectedCount: Int
        get() = mediaItems.size
    
    val formattedPosition: String
        get() = "${currentIndex + 1} of ${mediaItems.size}"
    
    val playbackProgress: Float
        get() = if (playbackDuration > 0) {
            (playbackPosition.toFloat() / playbackDuration.toFloat()).coerceIn(0f, 1f)
        } else 0f
}
