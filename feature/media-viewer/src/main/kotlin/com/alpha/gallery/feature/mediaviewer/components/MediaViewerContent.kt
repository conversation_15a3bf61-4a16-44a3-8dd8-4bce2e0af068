package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.rememberTransformableState
import androidx.compose.foundation.gestures.transformable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.request.ImageRequest
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.feature.mediaviewer.util.MediaLoadingUtils

/**
 * Main content area for displaying media (images and videos)
 */
@Composable
fun MediaViewerContent(
    mediaItem: MediaItem,
    isPlaying: Boolean = false,
    playbackPosition: Long = 0L,
    playbackDuration: Long = 0L,
    isBuffering: Boolean = false,
    zoomScale: Float = 1f,
    onPlayPause: () -> Unit = {},
    onSeek: (Long) -> Unit = {},
    onZoom: (Float) -> Unit = {},
    onTap: () -> Unit = {},
    onPlaybackUpdate: (Boolean, Long, Long, Boolean) -> Unit = { _, _, _, _ -> },
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onTap() }
                )
            }
    ) {
        if (mediaItem.isVideo) {
            // Video content
            VideoPlayerContent(
                mediaItem = mediaItem,
                isPlaying = isPlaying,
                playbackPosition = playbackPosition,
                playbackDuration = playbackDuration,
                isBuffering = isBuffering,
                onPlayPause = onPlayPause,
                onSeek = onSeek,
                onPlaybackUpdate = onPlaybackUpdate,
                modifier = Modifier.fillMaxSize()
            )
        } else {
            // Image content with zoom support
            ZoomableImage(
                mediaItem = mediaItem,
                onTap = onTap,
                onZoomChange = onZoom,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * Zoomable image content
 */
@Composable
private fun ZoomableImageContent(
    mediaItem: MediaItem,
    zoomScale: Float,
    onZoom: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var scale by remember { mutableFloatStateOf(zoomScale) }
    var offset by remember { mutableStateOf(Offset.Zero) }
    
    val transformableState = rememberTransformableState { zoomChange, offsetChange, _ ->
        val newScale = (scale * zoomChange).coerceIn(0.5f, 5f)
        scale = newScale
        offset += offsetChange
        onZoom(newScale)
    }
    
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        AsyncImage(
            model = MediaLoadingUtils.createImageRequest(context, mediaItem),
            contentDescription = mediaItem.name,
            contentScale = ContentScale.Fit,
            onState = { state ->
                isLoading = state is AsyncImagePainter.State.Loading
                hasError = state is AsyncImagePainter.State.Error
            },
            modifier = Modifier
                .fillMaxSize()
                .clip(RectangleShape)
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    translationX = offset.x,
                    translationY = offset.y
                )
                .transformable(state = transformableState)
        )

        // Loading indicator
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }

        // Error state
        if (hasError) {
            // Show a simple error indicator - the parent will handle the error state
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * Video player content
 */
@Composable
private fun VideoPlayerContent(
    mediaItem: MediaItem,
    isPlaying: Boolean,
    playbackPosition: Long,
    playbackDuration: Long,
    isBuffering: Boolean,
    onPlayPause: () -> Unit,
    onSeek: (Long) -> Unit,
    onPlaybackUpdate: (Boolean, Long, Long, Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    VideoPlayer(
        uri = mediaItem.uri,
        isPlaying = isPlaying,
        playbackPosition = playbackPosition,
        onPlaybackUpdate = onPlaybackUpdate,
        modifier = modifier
    )
}

/**
 * Placeholder for video player (to be replaced with ExoPlayer)
 */
@Composable
private fun VideoPlayerPlaceholder(
    mediaItem: MediaItem,
    isPlaying: Boolean,
    isBuffering: Boolean,
    onPlayPause: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // Show video thumbnail
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(mediaItem.uri)
                .crossfade(true)
                .build(),
            contentDescription = mediaItem.name,
            contentScale = ContentScale.Fit,
            modifier = Modifier.fillMaxSize()
        )
        
        // Show buffering indicator
        if (isBuffering) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}
