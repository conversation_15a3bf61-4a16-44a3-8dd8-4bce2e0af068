package com.alpha.gallery.feature.mediaviewer

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerContent
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerControls
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerErrorState
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerLoadingState
import com.alpha.gallery.feature.mediaviewer.components.MediaViewerMetadata
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiAction
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiEvent
import com.alpha.gallery.feature.mediaviewer.util.SystemUiController

/**
 * Main Media Viewer Screen
 */
@Composable
fun MediaViewerScreen(
    mediaItemId: String,
    onNavigateBack: () -> Unit = {},
    onShowMessage: (String) -> Unit = {},
    onShowError: (String) -> Unit = {},
    viewModel: MediaViewerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Initialize with media item ID
    LaunchedEffect(mediaItemId) {
        viewModel.initialize(mediaItemId)
    }
    
    // Handle UI actions
    LaunchedEffect(viewModel.uiActions) {
        viewModel.uiActions.collect { action ->
            when (action) {
                is MediaViewerUiAction.NavigateBack -> {
                    onNavigateBack()
                }
                is MediaViewerUiAction.ShowMessage -> {
                    onShowMessage(action.message)
                }
                is MediaViewerUiAction.ShowError -> {
                    onShowError(action.error)
                }
                is MediaViewerUiAction.ShareMedia -> {
                    // TODO: Implement share functionality
                    onShowMessage("Share functionality coming soon")
                }
                is MediaViewerUiAction.RequestStoragePermission -> {
                    // TODO: Handle permission request
                    onShowError("Storage permission required")
                }
                is MediaViewerUiAction.HideSystemUI -> {
                    // TODO: Hide system UI for fullscreen
                }
                is MediaViewerUiAction.ShowSystemUI -> {
                    // TODO: Show system UI
                }
            }
        }
    }
    
    // Handle back button
    BackHandler {
        viewModel.onEvent(MediaViewerUiEvent.NavigateBack)
    }
    
    // Handle lifecycle events
    LaunchedEffect(Unit) {
        viewModel.onEvent(MediaViewerUiEvent.OnResume)
    }

    // Handle system UI
    SystemUiController(
        isFullscreen = uiState.isFullscreen,
        keepScreenOn = true
    )
    
    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        containerColor = Color.Black
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
                .padding(paddingValues)
        ) {
            when {
                // Show loading state
                uiState.showLoadingState -> {
                    MediaViewerLoadingState(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                // Show error state
                uiState.showError -> {
                    MediaViewerErrorState(
                        error = uiState.error ?: "Unknown error",
                        onRetry = { viewModel.onEvent(MediaViewerUiEvent.RetryLoad) },
                        onNavigateBack = { viewModel.onEvent(MediaViewerUiEvent.NavigateBack) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                // Show content
                uiState.showContent -> {
                    // Main media content
                    MediaViewerContent(
                        mediaItem = uiState.currentMediaItem!!,
                        isPlaying = uiState.isPlaying,
                        playbackPosition = uiState.playbackPosition,
                        playbackDuration = uiState.playbackDuration,
                        isBuffering = uiState.isBuffering,
                        zoomScale = uiState.zoomScale,
                        onPlayPause = { viewModel.onEvent(MediaViewerUiEvent.TogglePlayPause) },
                        onSeek = { position -> viewModel.onEvent(MediaViewerUiEvent.SeekTo(position)) },
                        onZoom = { scale -> viewModel.onEvent(MediaViewerUiEvent.ZoomTo(scale)) },
                        onTap = { viewModel.onEvent(MediaViewerUiEvent.ToggleControls) },
                        onPlaybackUpdate = { isPlaying, position, duration, isBuffering ->
                            viewModel.updatePlaybackState(isPlaying, position, duration, isBuffering)
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                    
                    // Controls overlay with animation
                    AnimatedVisibility(
                        visible = uiState.showControls,
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        MediaViewerControls(
                            mediaItem = uiState.currentMediaItem!!,
                            isPlaying = uiState.isPlaying,
                            playbackPosition = uiState.playbackPosition,
                            playbackDuration = uiState.playbackDuration,
                            playbackProgress = uiState.playbackProgress,
                            canNavigatePrevious = uiState.canNavigatePrevious,
                            canNavigateNext = uiState.canNavigateNext,
                            formattedPosition = uiState.formattedPosition,
                            isFullscreen = uiState.isFullscreen,
                            onNavigateBack = { viewModel.onEvent(MediaViewerUiEvent.NavigateBack) },
                            onNavigatePrevious = { viewModel.onEvent(MediaViewerUiEvent.NavigatePrevious) },
                            onNavigateNext = { viewModel.onEvent(MediaViewerUiEvent.NavigateNext) },
                            onPlayPause = { viewModel.onEvent(MediaViewerUiEvent.TogglePlayPause) },
                            onSeek = { position -> viewModel.onEvent(MediaViewerUiEvent.SeekTo(position)) },
                            onToggleFavorite = { viewModel.onEvent(MediaViewerUiEvent.ToggleFavorite) },
                            onShare = { viewModel.onEvent(MediaViewerUiEvent.ShareMedia) },
                            onDelete = { viewModel.onEvent(MediaViewerUiEvent.DeleteMedia) },
                            onToggleMetadata = { viewModel.onEvent(MediaViewerUiEvent.ToggleMetadata) },
                            onToggleFullscreen = { viewModel.onEvent(MediaViewerUiEvent.ToggleFullscreen) },
                            modifier = Modifier.fillMaxSize()
                        )
                    }

                    // Metadata overlay with animation
                    AnimatedVisibility(
                        visible = uiState.showMetadata,
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        MediaViewerMetadata(
                            mediaItem = uiState.currentMediaItem!!,
                            onDismiss = { viewModel.onEvent(MediaViewerUiEvent.ToggleMetadata) },
                            modifier = Modifier.align(Alignment.BottomCenter)
                        )
                    }
                }
            }
        }
    }
}
