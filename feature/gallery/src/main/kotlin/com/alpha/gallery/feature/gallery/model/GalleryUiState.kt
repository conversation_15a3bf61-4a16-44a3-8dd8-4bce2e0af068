package com.alpha.gallery.feature.gallery.model

import com.alpha.gallery.core.domain.model.MediaItem

/**
 * UI state for the Gallery screen
 */
data class GalleryUiState(
    val mediaItems: List<MediaItem> = emptyList(),
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val error: String? = null,
    val isEmpty: Boolean = false,
    val hasPermissions: Boolean = false,
    val isSyncInProgress: Boolean = false,
    val lastSyncTime: Long = 0L,
    val selectedItems: Set<String> = emptySet(),
    val isSelectionMode: Boolean = false,
    val gridColumns: Int = 5
) {
    val showEmptyState: Boolean
        get() = isEmpty && !isLoading && !isRefreshing && hasPermissions
    
    val showLoadingState: Boolean
        get() = isLoading && mediaItems.isEmpty()
    
    val showContent: Boolean
        get() = mediaItems.isNotEmpty() && hasPermissions
    
    val showError: <PERSON><PERSON><PERSON>
        get() = error != null && !isLoading
    
    val selectedCount: Int
        get() = selectedItems.size
}

/**
 * UI events for the Gallery screen
 */
sealed class GalleryUiEvent {
    object Refresh : GalleryUiEvent()
    object RetryLoad : GalleryUiEvent()
    object ClearError : GalleryUiEvent()
    object ToggleSelectionMode : GalleryUiEvent()
    object ClearSelection : GalleryUiEvent()
    data class SelectItem(val itemId: String) : GalleryUiEvent()
    data class DeselectItem(val itemId: String) : GalleryUiEvent()
    data class OpenMediaItem(val itemId: String) : GalleryUiEvent()
    data class ToggleFavorite(val itemId: String) : GalleryUiEvent()
    data class DeleteItems(val itemIds: Set<String>) : GalleryUiEvent()
    data class ChangeGridColumns(val columns: Int) : GalleryUiEvent()
}

/**
 * UI actions that need to be handled by the parent
 */
sealed class GalleryUiAction {
    data class NavigateToMediaViewer(val itemId: String) : GalleryUiAction()
    data class ShowMessage(val message: String) : GalleryUiAction()
    data class ShowError(val error: String) : GalleryUiAction()
}
