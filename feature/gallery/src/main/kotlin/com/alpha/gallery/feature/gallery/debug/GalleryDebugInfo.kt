package com.alpha.gallery.feature.gallery.debug

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.feature.gallery.GalleryViewModel
import javax.inject.Inject

/**
 * Debug screen to help diagnose gallery issues
 */
@Composable
fun GalleryDebugInfo(
    permissionManager: PermissionManager,
    viewModel: GalleryViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Gallery Debug Information",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Divider()
        
        // Permission Status
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "Permissions",
                    style = MaterialTheme.typography.titleMedium
                )
                
                val permissionStatus = permissionManager.getPermissionStatus()
                Text("Android Version: ${permissionStatus.androidVersion}")
                Text("Has All Permissions: ${permissionStatus.hasAllPermissions}")
                Text("Required: ${permissionStatus.requiredPermissions.joinToString()}")
                Text("Granted: ${permissionStatus.grantedPermissions.joinToString()}")
                Text("Missing: ${permissionStatus.missingPermissions.joinToString()}")
            }
        }
        
        // UI State
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "UI State",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Text("Has Permissions: ${uiState.hasPermissions}")
                Text("Is Loading: ${uiState.isLoading}")
                Text("Is Refreshing: ${uiState.isRefreshing}")
                Text("Is Empty: ${uiState.isEmpty}")
                Text("Error: ${uiState.error ?: "None"}")
                Text("Media Items Count: ${uiState.mediaItems.size}")
                Text("Selected Items: ${uiState.selectedItems.size}")
                Text("Is Selection Mode: ${uiState.isSelectionMode}")
                Text("Grid Columns: ${uiState.gridColumns}")
            }
        }
        
        // State Flags
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "Display States",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Text("Show Loading State: ${uiState.showLoadingState}")
                Text("Show Content: ${uiState.showContent}")
                Text("Show Error: ${uiState.showError}")
                Text("Show Empty State: ${uiState.showEmptyState}")
            }
        }
        
        // Media Items Sample
        if (uiState.mediaItems.isNotEmpty()) {
            Card {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = "Sample Media Items",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    uiState.mediaItems.take(3).forEach { item ->
                        Text("ID: ${item.id}")
                        Text("Name: ${item.name}")
                        Text("Type: ${if (item.isVideo) "Video" else "Image"}")
                        Text("URI: ${item.uri}")
                        Divider()
                    }
                }
            }
        }
        
        // Actions
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "Debug Actions",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Button(
                    onClick = { 
                        // Force refresh
                        viewModel.onEvent(com.alpha.gallery.feature.gallery.model.GalleryUiEvent.Refresh)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Force Refresh")
                }
                
                Button(
                    onClick = { 
                        // Retry load
                        viewModel.onEvent(com.alpha.gallery.feature.gallery.model.GalleryUiEvent.RetryLoad)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Retry Load")
                }
                
                if (uiState.mediaItems.isNotEmpty()) {
                    Button(
                        onClick = { 
                            // Test navigation with first item
                            viewModel.onEvent(
                                com.alpha.gallery.feature.gallery.model.GalleryUiEvent.OpenMediaItem(
                                    uiState.mediaItems.first().id
                                )
                            )
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Test Navigation (First Item)")
                    }
                }
            }
        }
    }
}
