package com.alpha.gallery.feature.gallery.components

import android.R.attr.value
import android.util.Log
import androidx.compose.animation.core.*
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.runtime.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.math.min

/**
 * State management for scrollbar visibility, animations, and interactions
 * The scrollbar remains visible during scrolling and hides 1.5s after scrolling ends (when not touching)
 */
@Stable
class ScrollbarState(
    val lazyGridState: LazyGridState,
    private val hideDelayMs: Long = 1500L
) {
    private var _isVisible by mutableStateOf(false)
    private var _isDragging by mutableStateOf(false)
    private var _isTouching by mutableStateOf(false)
    private var _thumbPosition by mutableFloatStateOf(0f)
    private var _thumbSize by mutableFloatStateOf(0f)
    private var hideJob: kotlinx.coroutines.Job? = null

    val isVisible: Boolean get() = _isVisible
    val isDragging: Boolean get() = _isDragging
    val isTouching: Boolean get() = _isTouching
    val thumbPosition: Float get() = _thumbPosition
    val thumbSize: Float get() = _thumbSize
    
    // Animation values - these need to be @Composable
    @Composable
    fun getAlpha(): Float {
        return animateFloatAsState(
            targetValue = if (_isVisible) 1f else 0f,
            animationSpec = if (_isVisible) {
                tween(200, easing = FastOutSlowInEasing)
            } else {
                tween(400, easing = FastOutSlowInEasing)
            },
            label = "scrollbar_alpha"
        ).value
    }

    @Composable
    fun getThumbScale(): Float {
        return animateFloatAsState(
            targetValue = if (_isDragging) 1.5f else 1f,
            animationSpec = tween(250, easing = FastOutSlowInEasing),
            label = "thumb_scale"
        ).value
    }

    @Composable
    fun getThumbElevation(): Float {
        return animateFloatAsState(
            targetValue = if (_isDragging) 4f else 2f,
            animationSpec = tween(200, easing = FastOutSlowInEasing),
            label = "thumb_elevation"
        ).value
    }
    
    /**
     * Update scrollbar state based on grid scroll position
     */
    fun updateScrollPosition(
        coroutineScope: CoroutineScope,
        trackHeight: Float,
        minThumbSize: Float
    ) {
        val layoutInfo = lazyGridState.layoutInfo
        val totalItems = layoutInfo.totalItemsCount

        if (totalItems == 0 || layoutInfo.visibleItemsInfo.isEmpty()) {
            Log.d(TAG, "updateScrollPosition: _isVisible = false, 1")
            _isVisible = false
            return
        }

        val firstVisibleItem = layoutInfo.visibleItemsInfo.first()
        val lastVisibleItem = layoutInfo.visibleItemsInfo.last()

        // Calculate scroll progress (0f to 1f)
        val scrollProgress = if (totalItems <= layoutInfo.visibleItemsInfo.size) {
            0f // All items are visible, no need for scrollbar
        } else {
            val firstItemIndex = firstVisibleItem.index.toFloat()
            val firstItemOffset = firstVisibleItem.offset.y.toFloat()
            val itemHeight = if (firstVisibleItem.size.height > 0) {
                firstVisibleItem.size.height.toFloat()
            } else 100f

            val scrollOffset = firstItemIndex + (firstItemOffset.coerceAtMost(0f) / -itemHeight)
            val maxScrollOffset = (totalItems - layoutInfo.visibleItemsInfo.size).toFloat()

            (scrollOffset / maxScrollOffset).coerceIn(0f, 1f)
        }

        // Calculate thumb size based on visible content ratio
        val visibleRatio = layoutInfo.visibleItemsInfo.size.toFloat() / totalItems.toFloat()
        val calculatedThumbSize = (trackHeight * visibleRatio).coerceAtLeast(minThumbSize)
        _thumbSize = min(calculatedThumbSize, trackHeight)

        // Calculate thumb position
        val maxThumbPosition = trackHeight - _thumbSize
        _thumbPosition = scrollProgress * maxThumbPosition

        // Show scrollbar if content is scrollable, keep it visible during scrolling
        val shouldShow = totalItems > layoutInfo.visibleItemsInfo.size
        if (shouldShow) {
            Log.d(TAG, "updateScrollPosition: _isVisible = true, 1")
            _isVisible = true
        } else {
            // Only hide when content is not scrollable
            Log.d(TAG, "updateScrollPosition: _isVisible = false, 2")
            _isVisible = false
        }
    }

    /**
     * Show scrollbar when scrolling is detected - remains visible during scrolling regardless of touch state
     */
    fun onScrollDetected(coroutineScope: CoroutineScope) {
        val layoutInfo = lazyGridState.layoutInfo
        val totalItems = layoutInfo.totalItemsCount
        val visibleItems = layoutInfo.visibleItemsInfo.size

        // Cancel any pending hide job since we're scrolling
        hideJob?.cancel()
        hideJob = null

        // Only show if content is scrollable, keep visible during scrolling
        if (totalItems > visibleItems) {
            Log.d(TAG, "onScrollDetected: _isVisible = true, cancelling hide job")
            _isVisible = true
        }
    }

    /**
     * Handle scroll end - start hide timer if not touching
     */
    fun onScrollEnd(coroutineScope: CoroutineScope) {
        val layoutInfo = lazyGridState.layoutInfo
        val totalItems = layoutInfo.totalItemsCount
        val visibleItems = layoutInfo.visibleItemsInfo.size

        // Only start hide timer if content is scrollable and not touching
        if (totalItems > visibleItems && !_isTouching && !_isDragging) {
            Log.d(TAG, "onScrollEnd: starting hide timer (${hideDelayMs}ms)")
            hideJob?.cancel()
            hideJob = coroutineScope.launch {
                delay(hideDelayMs)
                if (!_isTouching && !_isDragging) {
                    Log.d(TAG, "onScrollEnd: hiding scrollbar after delay")
                    _isVisible = false
                }
            }
        }
    }
    
    /**
     * Start dragging the scrollbar thumb
     */
    fun startDrag() {
        _isDragging = true
        _isTouching = true
        // Cancel any pending hide job since we're touching
        hideJob?.cancel()
        hideJob = null
        Log.d(TAG, "startDrag: _isVisible = true, _isTouching = true")
        _isVisible = true
    }

    /**
     * Stop dragging the scrollbar thumb
     */
    fun stopDrag(coroutineScope: CoroutineScope) {
        _isDragging = false
        _isTouching = false

        // Start hide timer after dragging ends if not scrolling
        if (!lazyGridState.isScrollInProgress) {
            onScrollEnd(coroutineScope)
        }
        Log.d(TAG, "stopDrag: _isDragging = false, _isTouching = false")
    }

    /**
     * Set touch state for scrollbar interaction
     */
    fun setTouchState(isTouching: Boolean, coroutineScope: CoroutineScope) {
        _isTouching = isTouching

        if (isTouching) {
            // Cancel hide job when touching starts
            hideJob?.cancel()
            hideJob = null
            Log.d(TAG, "setTouchState: touching started, cancelling hide job")
        } else {
            // Start hide timer when touching ends if not scrolling or dragging
            if (!lazyGridState.isScrollInProgress && !_isDragging) {
                onScrollEnd(coroutineScope)
            }
            Log.d(TAG, "setTouchState: touching ended")
        }
    }
    
    /**
     * Handle drag position change and scroll the grid accordingly
     */
    suspend fun onDragPositionChange(
        dragY: Float,
        trackHeight: Float,
        coroutineScope: CoroutineScope
    ) {
        val maxThumbPosition = trackHeight - _thumbSize
        val clampedPosition = dragY.coerceIn(0f, maxThumbPosition)
        _thumbPosition = clampedPosition
        
        // Calculate target scroll position
        val scrollProgress = if (maxThumbPosition > 0) {
            clampedPosition / maxThumbPosition
        } else 0f
        
        val layoutInfo = lazyGridState.layoutInfo
        val totalItems = layoutInfo.totalItemsCount
        val visibleItems = layoutInfo.visibleItemsInfo.size
        
        if (totalItems > visibleItems) {
            val maxScrollIndex = totalItems - visibleItems
            val targetIndex = (scrollProgress * maxScrollIndex).toInt().coerceIn(0, maxScrollIndex)
            
            coroutineScope.launch {
                lazyGridState.animateScrollToItem(targetIndex)
            }
        }
    }
    
    /**
     * Handle track click to jump to position
     */
    suspend fun onTrackClick(
        clickY: Float,
        trackHeight: Float,
        coroutineScope: CoroutineScope
    ) {
        val targetPosition = clickY - (_thumbSize / 2f)
        onDragPositionChange(targetPosition, trackHeight, coroutineScope)
    }
    
    /**
     * Show scrollbar (e.g., on scroll start) - keeps visible during scrolling
     */
    fun show(coroutineScope: CoroutineScope) {
        val layoutInfo = lazyGridState.layoutInfo
        val totalItems = layoutInfo.totalItemsCount
        val visibleItems = layoutInfo.visibleItemsInfo.size

        // Cancel any pending hide job
        hideJob?.cancel()
        hideJob = null

        // Only show if content is scrollable, keep visible during scrolling
        if (totalItems > visibleItems) {
            Log.d(TAG, "show: _isVisible = true, cancelling hide job")
            _isVisible = true
        }
    }

    companion object {
        private const val TAG = "ScrollbarState"
    }
}

/**
 * Remember scrollbar state tied to a LazyGridState
 */
@Composable
fun rememberScrollbarState(
    lazyGridState: LazyGridState,
    hideDelayMs: Long = 1500L
): ScrollbarState {
    return remember(lazyGridState) {
        ScrollbarState(lazyGridState, hideDelayMs)
    }
}
