package com.alpha.gallery.feature.gallery

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alpha.gallery.feature.gallery.components.*
import com.alpha.gallery.feature.gallery.model.GalleryUiEvent
import com.alpha.gallery.feature.gallery.model.GalleryUiAction

/**
 * Main Gallery Screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryScreen(
    onNavigateToMediaViewer: (String) -> Unit = {},
    onShowMessage: (String) -> Unit = {},
    onShowError: (String) -> Unit = {},
    viewModel: GalleryViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // Handle UI actions
    LaunchedEffect(viewModel.uiActions) {
        viewModel.uiActions.collect { action ->
            when (action) {
                is GalleryUiAction.NavigateToMediaViewer -> {
                    onNavigateToMediaViewer(action.itemId)
                }
                is GalleryUiAction.ShowMessage -> {
                    onShowMessage(action.message)
                }
                is GalleryUiAction.ShowError -> {
                    onShowError(action.error)
                }
            }
        }
    }
    
    Scaffold(
        topBar = {
            GalleryTopBar(
                isSelectionMode = uiState.isSelectionMode,
                selectedCount = uiState.selectedCount,
                gridColumns = uiState.gridColumns,
                onToggleSelectionMode = { viewModel.onEvent(GalleryUiEvent.ToggleSelectionMode) },
                onClearSelection = { viewModel.onEvent(GalleryUiEvent.ClearSelection) },
                onChangeGridColumns = { columns -> 
                    viewModel.onEvent(GalleryUiEvent.ChangeGridColumns(columns))
                },
                onRefresh = { viewModel.onEvent(GalleryUiEvent.Refresh) }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                // Show loading state
                uiState.showLoadingState -> {
                    MediaGridLoadingState()
                }
                
                // Show permission required state
                !uiState.hasPermissions -> {
                    MediaGridPermissionRequiredState(
                        onRequestPermission = {
                            // This would typically trigger permission request
                            // For now, just show a message
                            onShowMessage("Please grant media permissions in app settings")
                        }
                    )
                }
                
                // Show error state
                uiState.showError -> {
                    MediaGridErrorState(
                        error = uiState.error ?: "Unknown error",
                        onRetry = { viewModel.onEvent(GalleryUiEvent.RetryLoad) }
                    )
                }
                
                // Show empty state
                uiState.showEmptyState -> {
                    MediaGridEmptyState(
                        title = "No media found",
                        subtitle = "Your photos and videos will appear here once media sync is complete"
                    )
                }
                
                // Show content
                uiState.showContent -> {
                    MediaGridWithLoading(
                        mediaItems = uiState.mediaItems,
                        isLoading = uiState.isLoading,
                        columns = uiState.gridColumns,
                        selectedItems = uiState.selectedItems,
                        isSelectionMode = uiState.isSelectionMode,
                        onItemClick = { itemId ->
                            viewModel.onEvent(GalleryUiEvent.OpenMediaItem(itemId))
                        },
                        onItemLongClick = { itemId ->
                            if (!uiState.isSelectionMode) {
                                viewModel.onEvent(GalleryUiEvent.ToggleSelectionMode)
                            }
                            viewModel.onEvent(GalleryUiEvent.SelectItem(itemId))
                        },
                        contentPadding = PaddingValues(4.dp)
                    )
                }
            }
        }
    }
}

/**
 * Top bar for the Gallery screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun GalleryTopBar(
    isSelectionMode: Boolean,
    selectedCount: Int,
    gridColumns: Int,
    onToggleSelectionMode: () -> Unit,
    onClearSelection: () -> Unit,
    onChangeGridColumns: (Int) -> Unit,
    onRefresh: () -> Unit
) {
    TopAppBar(
        title = {
            Text(
                text = if (isSelectionMode) {
                    "$selectedCount selected"
                } else {
                    "Gallery"
                }
            )
        },
        actions = {
            if (isSelectionMode) {
                // Selection mode actions
                IconButton(onClick = onClearSelection) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear selection"
                    )
                }
            } else {
                // Normal mode actions
                
                // Grid columns selector
                var showGridMenu by remember { mutableStateOf(false) }
                
                IconButton(onClick = { showGridMenu = true }) {
                    Icon(
                        imageVector = Icons.Default.Menu,
                        contentDescription = "Change grid layout"
                    )
                }
                
                DropdownMenu(
                    expanded = showGridMenu,
                    onDismissRequest = { showGridMenu = false }
                ) {
                    (2..5).forEach { columns ->
                        DropdownMenuItem(
                            text = { Text("$columns columns") },
                            onClick = {
                                onChangeGridColumns(columns)
                                showGridMenu = false
                            },
                            leadingIcon = if (columns == gridColumns) {
                                { Icon(Icons.Default.Check, contentDescription = null) }
                            } else null
                        )
                    }
                }
                
                // Refresh button
                IconButton(onClick = onRefresh) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Refresh"
                    )
                }
                
                // Selection mode toggle
                IconButton(onClick = onToggleSelectionMode) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Select items"
                    )
                }
            }
        }
    )
}
