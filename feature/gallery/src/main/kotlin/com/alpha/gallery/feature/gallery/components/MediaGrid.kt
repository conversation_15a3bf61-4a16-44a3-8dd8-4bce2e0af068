package com.alpha.gallery.feature.gallery.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.alpha.gallery.core.domain.model.MediaItem
import kotlinx.coroutines.launch

/**
 * Grid layout for displaying media items with scrollbar
 */
@Composable
fun MediaGrid(
    mediaItems: List<MediaItem>,
    columns: Int = 5,
    selectedItems: Set<String> = emptySet(),
    isSelectionMode: Boolean = false,
    onItemClick: (String) -> Unit,
    onItemLongClick: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    showScrollbar: Boolean = true,
    scrollbarConfig: ScrollbarConfig = ScrollbarConfig()
) {
    val lazyGridState = rememberLazyGridState()
    val coroutineScope = rememberCoroutineScope()
    val scrollbarState = rememberScrollbarState(lazyGridState, scrollbarConfig.hideDelayMs)

    // Monitor scroll state to show scrollbar immediately when scrolling starts and hide after scrolling ends
    LaunchedEffect(lazyGridState) {
        snapshotFlow { lazyGridState.isScrollInProgress }
            .collect { isScrolling ->
                if (isScrolling) {
                    scrollbarState.onScrollDetected(coroutineScope)
                } else {
                    // Scrolling ended, start hide timer if not touching
                    scrollbarState.onScrollEnd(coroutineScope)
                }
            }
    }

    Box(modifier = modifier) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(columns),
            state = lazyGridState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = contentPadding,
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            items(
                items = mediaItems,
                key = { it.id }
            ) { mediaItem ->
                MediaItem(
                    mediaItem = mediaItem,
                    isSelected = selectedItems.contains(mediaItem.id),
                    isSelectionMode = isSelectionMode,
                    onClick = { onItemClick(mediaItem.id) },
                    onLongClick = { onItemLongClick(mediaItem.id) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // Enhanced scrollbar with optimal touch experience
        if (showScrollbar) {
            AutoScrollbar(
                scrollbarState = scrollbarState,
                config = scrollbarConfig,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 4.dp)
            )
        }
    }
}

/**
 * Media grid with loading state and scrollbar
 */
@Composable
fun MediaGridWithLoading(
    mediaItems: List<MediaItem>,
    isLoading: Boolean = false,
    columns: Int = 5,
    selectedItems: Set<String> = emptySet(),
    isSelectionMode: Boolean = false,
    onItemClick: (String) -> Unit,
    onItemLongClick: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    showScrollbar: Boolean = true,
    scrollbarConfig: ScrollbarConfig = ScrollbarConfig()
) {
    val lazyGridState = rememberLazyGridState()
    val coroutineScope = rememberCoroutineScope()
    val scrollbarState = rememberScrollbarState(lazyGridState, scrollbarConfig.hideDelayMs)

    // Monitor scroll state to show scrollbar immediately when scrolling starts and hide after scrolling ends
    LaunchedEffect(lazyGridState) {
        snapshotFlow { lazyGridState.isScrollInProgress }
            .collect { isScrolling ->
                if (isScrolling) {
                    scrollbarState.onScrollDetected(coroutineScope)
                } else {
                    // Scrolling ended, start hide timer if not touching
                    scrollbarState.onScrollEnd(coroutineScope)
                }
            }
    }

    Box(modifier = modifier) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(columns),
            state = lazyGridState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = contentPadding,
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // Actual media items
            items(
                items = mediaItems,
                key = { it.id }
            ) { mediaItem ->
                MediaItem(
                    mediaItem = mediaItem,
                    isSelected = selectedItems.contains(mediaItem.id),
                    isSelectionMode = isSelectionMode,
                    onClick = { onItemClick(mediaItem.id) },
                    onLongClick = { onItemLongClick(mediaItem.id) },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Loading placeholders
            if (isLoading) {
                items(12) { // Show 12 loading placeholders
                    MediaItemWithLoading(
                        mediaItem = null,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        // Enhanced scrollbar with optimal touch experience
        if (showScrollbar) {
            AutoScrollbar(
                scrollbarState = scrollbarState,
                config = scrollbarConfig,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 4.dp)
            )
        }
    }
}

/**
 * Empty state for media grid
 */
@Composable
fun MediaGridEmptyState(
    title: String = "No media found",
    subtitle: String = "Your photos and videos will appear here",
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = subtitle,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Error state for media grid
 */
@Composable
fun MediaGridErrorState(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Something went wrong",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(onClick = onRetry) {
            Text("Retry")
        }
    }
}

/**
 * Loading state for media grid
 */
@Composable
fun MediaGridLoadingState(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Loading your media...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Permission required state for media grid
 */
@Composable
fun MediaGridPermissionRequiredState(
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Media Access Required",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Grant media permissions to view your photos and videos",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(onClick = onRequestPermission) {
            Text("Grant Permission")
        }
    }
}
