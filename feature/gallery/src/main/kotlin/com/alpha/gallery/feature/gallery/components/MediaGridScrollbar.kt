package com.alpha.gallery.feature.gallery.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.semantics.*
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.offset
import kotlinx.coroutines.launch

/**
 * Enhanced scrollbar component for media grid with optimal touch experience
 */
@Composable
fun MediaGridScrollbar(
    scrollbarState: ScrollbarState,
    modifier: Modifier = Modifier,
    scrollbarWidth: Dp = 42.dp,
    scrollbarPadding: Dp = 6.dp,
    touchPadding: Dp = 12.dp,
    minThumbSize: Dp = 48.dp,
    thumbColor: Color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
    thumbColorDragging: Color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.95f)
) {
    val density = LocalDensity.current
    val hapticFeedback = LocalHapticFeedback.current
    val coroutineScope = rememberCoroutineScope()
    
    // Convert dp values to pixels for calculations
    val scrollbarWidthPx = with(density) { scrollbarWidth.toPx() }
    val scrollbarPaddingPx = with(density) { scrollbarPadding.toPx() }
    val touchPaddingPx = with(density) { touchPadding.toPx() }
    val minThumbSizePx = with(density) { minThumbSize.toPx() }
    
    BoxWithConstraints(
        modifier = modifier
            .fillMaxHeight()
            .width(scrollbarWidth + touchPadding * 2)
            .alpha(scrollbarState.getAlpha())
    ) {
        val trackHeight = with(density) { maxHeight.toPx() - scrollbarPaddingPx * 2 }
        
        // Update scroll position when composition changes
        LaunchedEffect(scrollbarState) {
            scrollbarState.updateScrollPosition(
                coroutineScope = coroutineScope,
                trackHeight = trackHeight,
                minThumbSize = minThumbSizePx
            )
        }
        
        // Scrollbar track (invisible, for click handling)
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            // Set touching state when interaction starts
                            scrollbarState.setTouchState(true, coroutineScope)

                            val clickY = offset.y - scrollbarPaddingPx
                            val thumbTop = scrollbarState.thumbPosition
                            val thumbBottom = thumbTop + scrollbarState.thumbSize

                            if (clickY in thumbTop..thumbBottom) {
                                // Start dragging thumb
                                scrollbarState.startDrag()
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            } else {
                                // Click on track to jump
                                coroutineScope.launch {
                                    scrollbarState.onTrackClick(
                                        clickY = clickY,
                                        trackHeight = trackHeight,
                                        coroutineScope = coroutineScope
                                    )
                                }
                            }
                        },
                        onDragEnd = {
                            if (scrollbarState.isDragging) {
                                scrollbarState.stopDrag(coroutineScope)
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                            } else {
                                // End touching state when interaction ends (for track clicks)
                                scrollbarState.setTouchState(false, coroutineScope)
                            }
                        },
                        onDrag = { _, dragAmount ->
                            if (scrollbarState.isDragging) {
                                val newPosition = scrollbarState.thumbPosition + dragAmount.y
                                coroutineScope.launch {
                                    scrollbarState.onDragPositionChange(
                                        dragY = newPosition,
                                        trackHeight = trackHeight,
                                        coroutineScope = coroutineScope
                                    )
                                }
                            }
                        }
                    )
                }
        )
        
        // Scrollbar thumb
        if (scrollbarState.isVisible && scrollbarState.thumbSize > 0) {
            val currentThumbColor by animateColorAsState(
                targetValue = if (scrollbarState.isDragging) thumbColorDragging else thumbColor,
                animationSpec = tween(200, easing = FastOutSlowInEasing),
                label = "thumb_color"
            )
            
            Box(
                modifier = Modifier
                    .offset(
                        x = touchPadding,
                        y = with(density) { 
                            (scrollbarPadding.toPx() + scrollbarState.thumbPosition).toDp() 
                        }
                    )
                    .size(
                        width = scrollbarWidth,
                        height = with(density) { 
                            scrollbarState.thumbSize.coerceAtLeast(minThumbSizePx).toDp() 
                        }
                    )
                    .scale(scrollbarState.getThumbScale())
                    .shadow(
                        elevation = with(density) { scrollbarState.getThumbElevation().toDp() },
                        shape = RoundedCornerShape(8.dp)
                    )
                    .clip(RoundedCornerShape(8.dp))
                    .background(currentThumbColor)
                    .semantics {
                        contentDescription = if (scrollbarState.isDragging) {
                            "Scrollbar thumb, dragging"
                        } else {
                            "Scrollbar thumb, drag to scroll"
                        }
                        role = Role.Button
                    }
            )
        }
    }
}

/**
 * Scrollbar configuration data class
 */
data class ScrollbarConfig(
    val width: Dp = 42.dp,
    val padding: Dp = 6.dp,
    val touchPadding: Dp = 12.dp,
    val minThumbSize: Dp = 48.dp,
    val thumbColor: Color? = null,
    val thumbColorDragging: Color? = null,
    val hideDelayMs: Long = 1500L
)

/**
 * Enhanced scrollbar with configuration support
 */
@Composable
fun MediaGridScrollbar(
    scrollbarState: ScrollbarState,
    config: ScrollbarConfig = ScrollbarConfig(),
    modifier: Modifier = Modifier
) {
    val thumbColor = config.thumbColor 
        ?: MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
    val thumbColorDragging = config.thumbColorDragging 
        ?: MaterialTheme.colorScheme.onSurface.copy(alpha = 0.95f)
    
    MediaGridScrollbar(
        scrollbarState = scrollbarState,
        modifier = modifier,
        scrollbarWidth = config.width,
        scrollbarPadding = config.padding,
        touchPadding = config.touchPadding,
        minThumbSize = config.minThumbSize,
        thumbColor = thumbColor,
        thumbColorDragging = thumbColorDragging
    )
}

/**
 * Scrollbar with automatic scroll detection
 */
@Composable
fun AutoScrollbar(
    scrollbarState: ScrollbarState,
    config: ScrollbarConfig = ScrollbarConfig(),
    modifier: Modifier = Modifier
) {
    val coroutineScope = rememberCoroutineScope()

    // Enhanced scroll detection - monitor scroll state changes
    LaunchedEffect(scrollbarState.lazyGridState) {
        // Combine multiple scroll indicators for comprehensive detection
        snapshotFlow {
            scrollbarState.lazyGridState.let { state ->
                // Create a composite key that changes when scrolling occurs
                "${state.firstVisibleItemIndex}-${state.firstVisibleItemScrollOffset}-${state.isScrollInProgress}"
            }
        }.collect { _ ->
            // Show scrollbar whenever any scroll-related state changes
            scrollbarState.onScrollDetected(coroutineScope)
        }
    }

    // Also monitor layout changes that might affect scrollability
    LaunchedEffect(scrollbarState.lazyGridState) {
        snapshotFlow {
            scrollbarState.lazyGridState.layoutInfo.totalItemsCount
        }.collect { _ ->
            // Update scrollbar when content changes
            scrollbarState.onScrollDetected(coroutineScope)
        }
    }

    MediaGridScrollbar(
        scrollbarState = scrollbarState,
        config = config,
        modifier = modifier
    )
}
