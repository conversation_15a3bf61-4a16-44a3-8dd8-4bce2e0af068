package com.alpha.gallery.feature.gallery.components

import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.runtime.Composable
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.Assert.*

/**
 * Tests for ScrollbarState scroll detection functionality
 */
class ScrollbarStateTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun `scrollbar state shows when scroll is detected`() = runTest {
        val lazyGridState = LazyGridState()
        val scrollbarState = ScrollbarState(lazyGridState)
        
        // Initially not visible
        assertFalse(scrollbarState.isVisible)
        
        // Simulate scroll detection
        scrollbarState.onScrollDetected(this)
        
        // Should be visible after scroll detection
        assertTrue(scrollbarState.isVisible)
    }

    @Test
    fun `scrollbar state hides after delay when not dragging`() = runTest {
        val lazyGridState = LazyGridState()
        val scrollbarState = ScrollbarState(lazyGridState, hideDelayMs = 100L)

        // Show scrollbar
        scrollbarState.onScrollDetected(this)
        assertTrue(scrollbarState.isVisible)

        // Wait for auto-hide delay
        testScheduler.advanceTimeBy(150L)

        // Should be hidden after delay
        assertFalse(scrollbarState.isVisible)
    }

    @Test
    fun `scrollbar state remains visible when dragging`() = runTest {
        val lazyGridState = LazyGridState()
        val scrollbarState = ScrollbarState(lazyGridState, hideDelayMs = 100L)

        // Show scrollbar and start dragging
        scrollbarState.onScrollDetected(this)
        scrollbarState.startDrag()
        assertTrue(scrollbarState.isVisible)
        assertTrue(scrollbarState.isDragging)

        // Wait for auto-hide delay
        testScheduler.advanceTimeBy(150L)

        // Should still be visible because dragging
        assertTrue(scrollbarState.isVisible)

        // Stop dragging
        scrollbarState.stopDrag(this)
        assertFalse(scrollbarState.isDragging)

        // Wait for auto-hide delay after stopping drag
        testScheduler.advanceTimeBy(150L)

        // Should be hidden now
        assertFalse(scrollbarState.isVisible)
    }

    @Test
    fun `scrollbar state updates position correctly`() = runTest {
        val lazyGridState = LazyGridState()
        val scrollbarState = ScrollbarState(lazyGridState)
        
        val trackHeight = 1000f
        val minThumbSize = 48f
        
        // Update scroll position
        scrollbarState.updateScrollPosition(this, trackHeight, minThumbSize)
        
        // Verify thumb size and position are calculated
        assertTrue(scrollbarState.thumbSize >= 0f)
        assertTrue(scrollbarState.thumbPosition >= 0f)
    }
}
