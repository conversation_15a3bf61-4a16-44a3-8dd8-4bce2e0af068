package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.SyncMetadataEntity

/**
 * Data Access Object for sync metadata
 */
@Dao
interface SyncMetadataDao {
    
    /**
     * Get sync metadata by key
     */
    @Query("SELECT * FROM sync_metadata WHERE key = :key")
    suspend fun getSyncMetadata(key: String): SyncMetadataEntity?
    
    /**
     * Get sync metadata value by key
     */
    @Query("SELECT value FROM sync_metadata WHERE key = :key")
    suspend fun getSyncMetadataValue(key: String): String?
    
    /**
     * Insert or update sync metadata
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncMetadata(metadata: SyncMetadataEntity)
    
    /**
     * Insert or update multiple sync metadata entries
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncMetadata(metadata: List<SyncMetadataEntity>)
    
    /**
     * Update sync metadata value
     */
    @Query("UPDATE sync_metadata SET value = :value, timestamp = :timestamp WHERE key = :key")
    suspend fun updateSyncMetadataValue(key: String, value: String, timestamp: Long = System.currentTimeMillis())
    
    /**
     * Delete sync metadata by key
     */
    @Query("DELETE FROM sync_metadata WHERE key = :key")
    suspend fun deleteSyncMetadata(key: String)
    
    /**
     * Get all sync metadata
     */
    @Query("SELECT * FROM sync_metadata ORDER BY timestamp DESC")
    suspend fun getAllSyncMetadata(): List<SyncMetadataEntity>
    
    /**
     * Clear all sync metadata
     */
    @Query("DELETE FROM sync_metadata")
    suspend fun clearAllSyncMetadata()
}
