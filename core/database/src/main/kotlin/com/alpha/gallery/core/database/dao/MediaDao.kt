package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.MediaEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for media items with optimized batch operations
 */
@Dao
interface MediaDao {
    
    /**
     * Get all media items ordered by date modified (newest first)
     */
    @Query("SELECT * FROM media_items WHERE is_deleted = 0 ORDER BY date_modified DESC")
    fun getAllMediaItems(): Flow<List<MediaEntity>>
    
    /**
     * Get media items by album ID
     */
    @Query("SELECT * FROM media_items WHERE album_id = :albumId AND is_deleted = 0 ORDER BY date_modified DESC")
    fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaEntity>>
    
    /**
     * Get media item by MediaStore ID
     */
    @Query("SELECT * FROM media_items WHERE media_store_id = :mediaStoreId AND is_deleted = 0")
    suspend fun getMediaItemByMediaStoreId(mediaStoreId: Long): MediaEntity?
    
    /**
     * Get media item by ID
     */
    @Query("SELECT * FROM media_items WHERE id = :id AND is_deleted = 0")
    suspend fun getMediaItemById(id: Long): MediaEntity?
    
    /**
     * Get recent media items with limit
     */
    @Query("SELECT * FROM media_items WHERE is_deleted = 0 ORDER BY date_added DESC LIMIT :limit")
    fun getRecentMediaItems(limit: Int): Flow<List<MediaEntity>>
    
    /**
     * Get favorite media items
     */
    @Query("SELECT * FROM media_items WHERE is_favorite = 1 AND is_deleted = 0 ORDER BY date_modified DESC")
    fun getFavoriteMediaItems(): Flow<List<MediaEntity>>
    
    /**
     * Search media items by display name
     */
    @Query("SELECT * FROM media_items WHERE display_name LIKE '%' || :query || '%' AND is_deleted = 0 ORDER BY date_modified DESC")
    fun searchMediaItems(query: String): Flow<List<MediaEntity>>
    
    /**
     * Get media items modified after timestamp (for incremental sync)
     */
    @Query("SELECT * FROM media_items WHERE date_modified > :timestamp ORDER BY date_modified ASC LIMIT :limit")
    suspend fun getMediaItemsModifiedAfter(timestamp: Long, limit: Int): List<MediaEntity>
    
    /**
     * Get media items by MediaStore IDs (for batch operations)
     */
    @Query("SELECT * FROM media_items WHERE media_store_id IN (:mediaStoreIds)")
    suspend fun getMediaItemsByMediaStoreIds(mediaStoreIds: List<Long>): List<MediaEntity>
    
    /**
     * Insert single media item with conflict resolution
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItem(mediaItem: MediaEntity): Long
    
    /**
     * Insert multiple media items in batch (optimized for sync)
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItems(mediaItems: List<MediaEntity>): List<Long>
    
    /**
     * Update media item
     */
    @Update
    suspend fun updateMediaItem(mediaItem: MediaEntity)
    
    /**
     * Update multiple media items in batch
     */
    @Update
    suspend fun updateMediaItems(mediaItems: List<MediaEntity>)
    
    /**
     * Mark media item as favorite
     */
    @Query("UPDATE media_items SET is_favorite = 1 WHERE id = :id")
    suspend fun markAsFavorite(id: Long)
    
    /**
     * Remove from favorites
     */
    @Query("UPDATE media_items SET is_favorite = 0 WHERE id = :id")
    suspend fun removeFromFavorites(id: Long)
    
    /**
     * Soft delete media item (mark as deleted instead of removing)
     */
    @Query("UPDATE media_items SET is_deleted = 1 WHERE id = :id")
    suspend fun softDeleteMediaItem(id: Long)
    
    /**
     * Soft delete media items by MediaStore IDs (for cleanup)
     */
    @Query("UPDATE media_items SET is_deleted = 1 WHERE media_store_id IN (:mediaStoreIds)")
    suspend fun softDeleteMediaItemsByMediaStoreIds(mediaStoreIds: List<Long>)
    
    /**
     * Hard delete media item (permanent removal)
     */
    @Delete
    suspend fun deleteMediaItem(mediaItem: MediaEntity)
    
    /**
     * Clean up deleted items older than timestamp
     */
    @Query("DELETE FROM media_items WHERE is_deleted = 1 AND sync_timestamp < :timestamp")
    suspend fun cleanupDeletedItems(timestamp: Long): Int
    
    /**
     * Get total media count
     */
    @Query("SELECT COUNT(*) FROM media_items WHERE is_deleted = 0")
    suspend fun getMediaCount(): Int
    
    /**
     * Get media count by type
     */
    @Query("SELECT COUNT(*) FROM media_items WHERE is_video = :isVideo AND is_deleted = 0")
    suspend fun getMediaCountByType(isVideo: Boolean): Int
    
    /**
     * Get last sync timestamp
     */
    @Query("SELECT MAX(sync_timestamp) FROM media_items")
    suspend fun getLastSyncTimestamp(): Long?
}
