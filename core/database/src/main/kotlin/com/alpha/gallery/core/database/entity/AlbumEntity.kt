package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Room entity for albums/folders
 */
@Entity(
    tableName = "albums",
    indices = [
        Index(value = ["bucket_id"], unique = true),
        Index(value = ["date_modified"]),
        Index(value = ["media_count"])
    ]
)
data class AlbumEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "bucket_id")
    val bucketId: String,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "path")
    val path: String,
    
    @ColumnInfo(name = "cover_image_path")
    val coverImagePath: String? = null,
    
    @ColumnInfo(name = "media_count")
    val mediaCount: Int = 0,
    
    @ColumnInfo(name = "date_added")
    val dateAdded: Long,
    
    @ColumnInfo(name = "date_modified")
    val dateModified: Long,
    
    @ColumnInfo(name = "is_cloud_album")
    val isCloudAlbum: Boolean = false,
    
    @ColumnInfo(name = "sync_timestamp")
    val syncTimestamp: Long = System.currentTimeMillis()
) {
    val displayName: String
        get() = if (name.isBlank()) "Unknown Album" else name
}
