package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.AlbumEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for albums
 */
@Dao
interface AlbumDao {
    
    /**
     * Get all albums ordered by media count (descending)
     */
    @Query("SELECT * FROM albums ORDER BY media_count DESC, name ASC")
    fun getAllAlbums(): Flow<List<AlbumEntity>>
    
    /**
     * Get album by bucket ID
     */
    @Query("SELECT * FROM albums WHERE bucket_id = :bucketId")
    suspend fun getAlbumByBucketId(bucketId: String): AlbumEntity?
    
    /**
     * Get album by ID
     */
    @Query("SELECT * FROM albums WHERE id = :id")
    suspend fun getAlbumById(id: Long): AlbumEntity?
    
    /**
     * Insert single album
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbum(album: AlbumEntity): Long
    
    /**
     * Insert multiple albums in batch
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbums(albums: List<AlbumEntity>): List<Long>
    
    /**
     * Update album
     */
    @Update
    suspend fun updateAlbum(album: AlbumEntity)
    
    /**
     * Update album media count
     */
    @Query("UPDATE albums SET media_count = :count WHERE bucket_id = :bucketId")
    suspend fun updateAlbumMediaCount(bucketId: String, count: Int)
    
    /**
     * Update album cover image
     */
    @Query("UPDATE albums SET cover_image_path = :coverImagePath WHERE bucket_id = :bucketId")
    suspend fun updateAlbumCoverImage(bucketId: String, coverImagePath: String?)
    
    /**
     * Delete album
     */
    @Delete
    suspend fun deleteAlbum(album: AlbumEntity)
    
    /**
     * Delete albums by bucket IDs
     */
    @Query("DELETE FROM albums WHERE bucket_id IN (:bucketIds)")
    suspend fun deleteAlbumsByBucketIds(bucketIds: List<String>)
    
    /**
     * Get albums with media count greater than zero
     */
    @Query("SELECT * FROM albums WHERE media_count > 0 ORDER BY media_count DESC, name ASC")
    fun getAlbumsWithMedia(): Flow<List<AlbumEntity>>
    
    /**
     * Get total album count
     */
    @Query("SELECT COUNT(*) FROM albums")
    suspend fun getAlbumCount(): Int
}
