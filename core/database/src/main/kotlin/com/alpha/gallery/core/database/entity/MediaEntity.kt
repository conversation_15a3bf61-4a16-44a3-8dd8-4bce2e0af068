package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Room entity for media items (images and videos)
 * 
 * Optimized for performance with proper indexing on frequently queried columns
 */
@Entity(
    tableName = "media_items",
    indices = [
        Index(value = ["media_store_id"], unique = true),
        Index(value = ["date_modified"]),
        Index(value = ["date_added"]),
        Index(value = ["album_id"]),
        Index(value = ["mime_type"]),
        Index(value = ["is_video"])
    ]
)
data class MediaEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "media_store_id")
    val mediaStoreId: Long,
    
    @ColumnInfo(name = "display_name")
    val displayName: String,
    
    @ColumnInfo(name = "file_path")
    val filePath: String,
    
    @ColumnInfo(name = "uri")
    val uri: String,
    
    @ColumnInfo(name = "mime_type")
    val mimeType: String,
    
    @ColumnInfo(name = "size")
    val size: Long,
    
    @ColumnInfo(name = "date_added")
    val dateAdded: Long,
    
    @ColumnInfo(name = "date_modified")
    val dateModified: Long,
    
    @ColumnInfo(name = "width")
    val width: Int = 0,
    
    @ColumnInfo(name = "height")
    val height: Int = 0,
    
    @ColumnInfo(name = "duration")
    val duration: Long = 0, // For videos, in milliseconds
    
    @ColumnInfo(name = "album_id")
    val albumId: String? = null,
    
    @ColumnInfo(name = "album_name")
    val albumName: String? = null,
    
    @ColumnInfo(name = "is_video")
    val isVideo: Boolean = mimeType.startsWith("video/"),
    
    @ColumnInfo(name = "thumbnail_path")
    val thumbnailPath: String? = null,
    
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,
    
    @ColumnInfo(name = "is_synced")
    val isSynced: Boolean = true,
    
    @ColumnInfo(name = "sync_timestamp")
    val syncTimestamp: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "is_deleted")
    val isDeleted: Boolean = false
) {
    val aspectRatio: Float
        get() = if (height > 0) width.toFloat() / height.toFloat() else 1f
}
