package com.alpha.gallery.core.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.alpha.gallery.core.common.Constants
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.dao.MediaDao
import com.alpha.gallery.core.database.dao.SyncMetadataDao
import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.database.entity.MediaEntity
import com.alpha.gallery.core.database.entity.SyncMetadataEntity

/**
 * Main Room database for the Gallery app
 * 
 * Includes all entities and provides access to DAOs
 */
@Database(
    entities = [
        MediaEntity::class,
        AlbumEntity::class,
        SyncMetadataEntity::class
    ],
    version = Constants.DATABASE_VERSION,
    exportSchema = true
)
abstract class GalleryDatabase : RoomDatabase() {
    
    abstract fun mediaDao(): MediaDao
    abstract fun albumDao(): AlbumDao
    abstract fun syncMetadataDao(): SyncMetadataDao
    
    companion object {
        
        /**
         * Create database instance with proper configuration
         */
        fun create(context: Context): GalleryDatabase {
            return Room.databaseBuilder(
                context.applicationContext,
                GalleryDatabase::class.java,
                Constants.DATABASE_NAME
            )
                .addMigrations(*getAllMigrations())
                .fallbackToDestructiveMigration() // Only for development
                .build()
        }
        
        /**
         * Get all database migrations
         */
        private fun getAllMigrations(): Array<Migration> {
            return arrayOf(
                // Add future migrations here
            )
        }
        
        /**
         * Migration from version 1 to 2 (example for future use)
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add migration logic here when needed
                // Example:
                // database.execSQL("ALTER TABLE media_items ADD COLUMN new_column TEXT")
            }
        }
    }
}
