package com.alpha.gallery.core.database.di

import android.content.Context
import com.alpha.gallery.core.database.GalleryDatabase
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.dao.MediaDao
import com.alpha.gallery.core.database.dao.SyncMetadataDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for database dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideGalleryDatabase(
        @ApplicationContext context: Context
    ): GalleryDatabase {
        return GalleryDatabase.create(context)
    }
    
    @Provides
    fun provideMediaDao(database: GalleryDatabase): MediaDao {
        return database.mediaDao()
    }
    
    @Provides
    fun provideAlbumDao(database: GalleryDatabase): AlbumDao {
        return database.albumDao()
    }
    
    @Provides
    fun provideSyncMetadataDao(database: GalleryDatabase): SyncMetadataDao {
        return database.syncMetadataDao()
    }
}
