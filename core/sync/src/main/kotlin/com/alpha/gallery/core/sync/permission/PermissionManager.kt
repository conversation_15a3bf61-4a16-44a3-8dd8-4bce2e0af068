package com.alpha.gallery.core.sync.permission

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages media permissions for different Android versions
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    /**
     * Check if app has required media permissions
     */
    fun hasMediaPermissions(): <PERSON><PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - Granular media permissions
            hasPermission(Manifest.permission.READ_MEDIA_IMAGES) &&
            hasPermission(Manifest.permission.READ_MEDIA_VIDEO)
        } else {
            // Android 12 and below - Legacy storage permission
            hasPermission(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * Check if app has image permissions specifically
     */
    fun hasImagePermissions(): <PERSON><PERSON><PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            hasPermission(Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            hasPermission(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * Check if app has video permissions specifically
     */
    fun hasVideoPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            hasPermission(Manifest.permission.READ_MEDIA_VIDEO)
        } else {
            hasPermission(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * Get required permissions for current Android version
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * Get missing permissions
     */
    fun getMissingPermissions(): List<String> {
        return getRequiredPermissions().filter { permission ->
            !hasPermission(permission)
        }
    }
    
    /**
     * Check if specific permission is granted
     */
    private fun hasPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Get permission status details for debugging
     */
    fun getPermissionStatus(): PermissionStatus {
        val requiredPermissions = getRequiredPermissions()
        val grantedPermissions = requiredPermissions.filter { hasPermission(it) }
        val missingPermissions = requiredPermissions.filter { !hasPermission(it) }

        return PermissionStatus(
            androidVersion = Build.VERSION.SDK_INT,
            requiredPermissions = requiredPermissions.toList(),
            grantedPermissions = grantedPermissions,
            missingPermissions = missingPermissions,
            hasAllPermissions = missingPermissions.isEmpty()
        )
    }

    /**
     * Check if we should show rationale for any missing permissions
     */
    fun shouldShowRationale(activity: android.app.Activity): Boolean {
        return getMissingPermissions().any { permission ->
            androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }

    /**
     * Create intent to open app settings for manual permission grant
     */
    fun createAppSettingsIntent(): Intent {
        return Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }

    /**
     * Get user-friendly permission names for display
     */
    fun getPermissionDisplayNames(): List<String> {
        return getRequiredPermissions().map { permission ->
            when (permission) {
                Manifest.permission.READ_MEDIA_IMAGES -> "Photos"
                Manifest.permission.READ_MEDIA_VIDEO -> "Videos"
                Manifest.permission.READ_EXTERNAL_STORAGE -> "Media Files"
                else -> permission.substringAfterLast(".")
            }
        }
    }

    /**
     * Get permission rationale message
     */
    fun getPermissionRationale(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            "This app needs access to your photos and videos to display them in the gallery."
        } else {
            "This app needs access to your device storage to display photos and videos in the gallery."
        }
    }
}

/**
 * Detailed permission status information
 */
data class PermissionStatus(
    val androidVersion: Int,
    val requiredPermissions: List<String>,
    val grantedPermissions: List<String>,
    val missingPermissions: List<String>,
    val hasAllPermissions: Boolean
)
