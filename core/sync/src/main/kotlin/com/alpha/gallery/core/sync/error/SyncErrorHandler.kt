package com.alpha.gallery.core.sync.error

import android.database.sqlite.SQLiteException
import com.alpha.gallery.core.sync.model.SyncResult
import com.alpha.gallery.core.sync.model.SyncType
import com.alpha.gallery.core.sync.util.SyncConstants
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles sync errors with retry logic and error categorization
 */
@Singleton
class SyncErrorHandler @Inject constructor() {
    
    /**
     * Handle sync error and return appropriate SyncResult
     */
    fun handleSyncError(
        exception: Throwable,
        syncType: SyncType,
        itemsProcessed: Int = 0
    ): SyncResult.Error {
        
        val errorCategory = categorizeError(exception)
        logError(exception, errorCategory, syncType)
        
        return SyncResult.Error(
            exception = SyncException(
                originalException = exception,
                category = errorCategory,
                syncType = syncType,
                isRetryable = isRetryableError(errorCategory)
            ),
            itemsProcessed = itemsProcessed,
            syncType = syncType
        )
    }
    
    /**
     * Determine if error is retryable
     */
    fun isRetryableError(category: ErrorCategory): Boolean {
        return when (category) {
            ErrorCategory.NETWORK_ERROR,
            ErrorCategory.TEMPORARY_IO_ERROR,
            ErrorCategory.DATABASE_BUSY,
            ErrorCategory.MEMORY_PRESSURE -> true

            ErrorCategory.PERMISSION_DENIED,
            ErrorCategory.STORAGE_NOT_AVAILABLE,
            ErrorCategory.DATABASE_ERROR,
            ErrorCategory.DATABASE_CORRUPTION,
            ErrorCategory.UNKNOWN_ERROR -> false
        }
    }
    
    /**
     * Calculate retry delay with exponential backoff
     */
    fun calculateRetryDelay(attemptNumber: Int): Long {
        val baseDelay = SyncConstants.ERROR_RETRY_DELAY_MS
        val maxDelay = SyncConstants.ERROR_MAX_RETRY_DELAY_MS
        val multiplier = SyncConstants.ERROR_BACKOFF_MULTIPLIER
        
        val delay = (baseDelay * Math.pow(multiplier.toDouble(), attemptNumber.toDouble())).toLong()
        return minOf(delay, maxDelay)
    }
    
    /**
     * Execute operation with retry logic
     */
    suspend fun <T> executeWithRetry(
        maxAttempts: Int = SyncConstants.MAX_RETRY_ATTEMPTS,
        operation: suspend (attemptNumber: Int) -> T
    ): T {
        var lastException: Exception? = null
        
        repeat(maxAttempts) { attempt ->
            try {
                return operation(attempt + 1)
            } catch (e: Exception) {
                lastException = e
                val errorCategory = categorizeError(e)
                
                if (!isRetryableError(errorCategory) || attempt == maxAttempts - 1) {
                    throw e
                }
                
                val retryDelay = calculateRetryDelay(attempt)
                delay(retryDelay)
            }
        }
        
        throw lastException ?: RuntimeException("Max retry attempts exceeded")
    }
    
    /**
     * Categorize error for appropriate handling
     */
    private fun categorizeError(exception: Throwable): ErrorCategory {
        return when (exception) {
            is SecurityException -> ErrorCategory.PERMISSION_DENIED
            is SQLiteException -> {
                when {
                    exception.message?.contains("database is locked") == true -> ErrorCategory.DATABASE_BUSY
                    exception.message?.contains("corrupt") == true -> ErrorCategory.DATABASE_CORRUPTION
                    else -> ErrorCategory.DATABASE_ERROR
                }
            }
            is java.io.IOException -> ErrorCategory.TEMPORARY_IO_ERROR
            is OutOfMemoryError -> ErrorCategory.MEMORY_PRESSURE
            is IllegalStateException -> {
                when {
                    exception.message?.contains("storage") == true -> ErrorCategory.STORAGE_NOT_AVAILABLE
                    else -> ErrorCategory.UNKNOWN_ERROR
                }
            }
            else -> ErrorCategory.UNKNOWN_ERROR
        }
    }
    
    /**
     * Log error with appropriate level and context
     */
    private fun logError(
        exception: Throwable,
        category: ErrorCategory,
        syncType: SyncType
    ) {
        val message = "Sync error occurred: ${exception.message}"
        val context = mapOf(
            "syncType" to syncType.name,
            "errorCategory" to category.name,
            "exceptionType" to exception::class.simpleName
        )
        
        // In a real app, you would use a proper logging framework
        when (category) {
            ErrorCategory.PERMISSION_DENIED,
            ErrorCategory.DATABASE_CORRUPTION -> {
                // Critical errors
                println("CRITICAL: $message - Context: $context")
            }
            ErrorCategory.NETWORK_ERROR,
            ErrorCategory.TEMPORARY_IO_ERROR,
            ErrorCategory.DATABASE_BUSY -> {
                // Recoverable errors
                println("WARNING: $message - Context: $context")
            }
            else -> {
                // General errors
                println("ERROR: $message - Context: $context")
            }
        }
    }
}

/**
 * Custom exception for sync operations
 */
class SyncException(
    val originalException: Throwable,
    val category: ErrorCategory,
    val syncType: SyncType,
    val isRetryable: Boolean,
    message: String = "Sync failed: ${originalException.message}"
) : Exception(message, originalException)

/**
 * Categories of sync errors
 */
enum class ErrorCategory {
    PERMISSION_DENIED,
    STORAGE_NOT_AVAILABLE,
    DATABASE_ERROR,
    DATABASE_BUSY,
    DATABASE_CORRUPTION,
    NETWORK_ERROR,
    TEMPORARY_IO_ERROR,
    MEMORY_PRESSURE,
    UNKNOWN_ERROR
}
