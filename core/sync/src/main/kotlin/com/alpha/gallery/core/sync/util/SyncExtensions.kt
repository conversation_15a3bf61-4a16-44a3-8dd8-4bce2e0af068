package com.alpha.gallery.core.sync.util

import android.database.Cursor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Extension functions for sync operations
 */

/**
 * Safely use cursor with automatic closing
 */
suspend inline fun <T> Cursor?.useSafely(crossinline block: (Cursor) -> T): T? = withContext(Dispatchers.IO) {
    this@useSafely?.use { cursor ->
        try {
            block(cursor)
        } catch (e: Exception) {
            println("Error using cursor: ${e.message}")
            null
        }
    }
}

/**
 * Get string value safely from cursor
 */
fun Cursor.getStringSafely(columnIndex: Int): String? {
    return try {
        if (columnIndex >= 0 && !isNull(columnIndex)) {
            getString(columnIndex)
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * Get long value safely from cursor
 */
fun Cursor.getLongSafely(columnIndex: Int): Long {
    return try {
        if (columnIndex >= 0 && !isNull(columnIndex)) {
            getLong(columnIndex)
        } else {
            0L
        }
    } catch (e: Exception) {
        0L
    }
}

/**
 * Get int value safely from cursor
 */
fun Cursor.getIntSafely(columnIndex: Int): Int {
    return try {
        if (columnIndex >= 0 && !isNull(columnIndex)) {
            getInt(columnIndex)
        } else {
            0
        }
    } catch (e: Exception) {
        0
    }
}

/**
 * Process items in chunks for memory efficiency
 */
suspend fun <T, R> List<T>.processInChunks(
    chunkSize: Int,
    processor: suspend (List<T>) -> R
): List<R> = withContext(Dispatchers.IO) {
    chunked(chunkSize).map { chunk ->
        processor(chunk)
    }
}

/**
 * Format file size for display
 */
fun Long.formatFileSize(): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = this.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return String.format("%.1f %s", size, units[unitIndex])
}

/**
 * Format duration for display
 */
fun Long.formatDuration(): String {
    if (this <= 0) return ""
    
    val seconds = this / 1000
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    
    return if (minutes > 0) {
        String.format("%02d:%02d", minutes, remainingSeconds)
    } else {
        String.format("0:%02d", remainingSeconds)
    }
}

/**
 * Check if timestamp is recent (within last hour)
 */
fun Long.isRecent(): Boolean {
    val oneHourAgo = System.currentTimeMillis() - (60 * 60 * 1000)
    return this > oneHourAgo
}

/**
 * Convert seconds to milliseconds safely
 */
fun Long.secondsToMillis(): Long {
    return try {
        this * 1000
    } catch (e: ArithmeticException) {
        0L
    }
}
