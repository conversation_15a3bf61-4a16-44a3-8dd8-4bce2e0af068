package com.alpha.gallery.core.sync.initializer

import android.content.Context
import com.alpha.gallery.core.sync.manager.MediaSyncManager
import com.alpha.gallery.core.sync.observer.MediaStoreObserver
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Initializes sync components and performs initial sync if needed
 */
@Singleton
class SyncInitializer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val permissionManager: PermissionManager,
    private val mediaSyncManager: MediaSyncManager,
    private val syncWorkScheduler: SyncWorkScheduler,
    private val mediaStoreObserver: MediaStoreObserver
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * Initialize sync system
     */
    fun initialize() {
        scope.launch {
            if (permissionManager.hasMediaPermissions()) {
                // Start MediaStore observer
                mediaStoreObserver.startObserving()
                
                // Schedule periodic sync
                syncWorkScheduler.schedulePeriodicSync()
                
                // Perform initial sync if needed
                performInitialSyncIfNeeded()
            }
        }
    }
    
    /**
     * Perform initial sync if this is the first app launch or after permission grant
     */
    private suspend fun performInitialSyncIfNeeded() {
        try {
            // Check if we need to perform initial sync
            if (shouldPerformInitialSync()) {
                // Trigger immediate sync
                syncWorkScheduler.scheduleImmediateSync()
            }
        } catch (e: Exception) {
            // Log error but don't crash
            println("Error during initial sync check: ${e.message}")
        }
    }
    
    /**
     * Determine if initial sync is needed
     */
    private suspend fun shouldPerformInitialSync(): Boolean {
        // For now, always perform initial sync
        // In a real app, you might check if database is empty or last sync was too long ago
        return true
    }
    
    /**
     * Handle permission changes
     */
    fun onPermissionsChanged() {
        scope.launch {
            if (permissionManager.hasMediaPermissions()) {
                // Restart observer
                mediaStoreObserver.restartObserver()
                
                // Trigger immediate sync
                syncWorkScheduler.scheduleImmediateSync()
            } else {
                // Stop observer if permissions are revoked
                mediaStoreObserver.stopObserving()
            }
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        mediaStoreObserver.cleanup()
    }
}
