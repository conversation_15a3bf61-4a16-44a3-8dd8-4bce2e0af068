package com.alpha.gallery.core.sync.util

import android.provider.MediaStore

/**
 * Constants for media synchronization operations
 */
object SyncConstants {
    
    // Sync Configuration
    const val SYNC_BATCH_SIZE = 100
    const val SYNC_INTERVAL_HOURS = 12L
    const val SYNC_FLEX_INTERVAL_HOURS = 2L
    const val SYNC_BACKOFF_DELAY_MILLIS = 30_000L // 30 seconds
    const val MAX_RETRY_ATTEMPTS = 3
    
    // WorkManager
    const val SYNC_WORK_NAME = "media_sync_work"
    const val PERIODIC_SYNC_WORK_NAME = "periodic_media_sync_work"
    const val IMMEDIATE_SYNC_WORK_NAME = "immediate_media_sync_work"
    
    // Database
    const val SYNC_METADATA_TABLE = "sync_metadata"
    const val MEDIA_TABLE = "media_items"
    const val ALBUM_TABLE = "albums"
    
    // Sync Keys
    const val LAST_SYNC_TIMESTAMP_KEY = "last_sync_timestamp"
    const val LAST_FULL_SYNC_KEY = "last_full_sync"
    const val SYNC_VERSION_KEY = "sync_version"
    const val CURRENT_SYNC_VERSION = 1
    
    // MediaStore Projections
    val MEDIA_PROJECTION = arrayOf(
        MediaStore.MediaColumns._ID,
        MediaStore.MediaColumns.DISPLAY_NAME,
        MediaStore.MediaColumns.DATA,
        MediaStore.MediaColumns.MIME_TYPE,
        MediaStore.MediaColumns.SIZE,
        MediaStore.MediaColumns.DATE_ADDED,
        MediaStore.MediaColumns.DATE_MODIFIED,
        MediaStore.MediaColumns.WIDTH,
        MediaStore.MediaColumns.HEIGHT,
        MediaStore.MediaColumns.DURATION,
        MediaStore.MediaColumns.BUCKET_ID,
        MediaStore.MediaColumns.BUCKET_DISPLAY_NAME
    )
    
    // Content URIs
    val EXTERNAL_CONTENT_URI = MediaStore.Files.getContentUri("external")
    val IMAGES_URI = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val VIDEOS_URI = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
    
    // MIME Types
    const val IMAGE_MIME_TYPE_PREFIX = "image/"
    const val VIDEO_MIME_TYPE_PREFIX = "video/"
    
    // Performance
    const val CURSOR_WINDOW_SIZE = 2048 * 1024 // 2MB
    const val MEMORY_THRESHOLD_MB = 50
    
    // Error Handling
    const val ERROR_RETRY_DELAY_MS = 1000L
    const val ERROR_MAX_RETRY_DELAY_MS = 30_000L
    const val ERROR_BACKOFF_MULTIPLIER = 2.0f
}
