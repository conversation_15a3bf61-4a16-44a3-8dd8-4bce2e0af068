package com.alpha.gallery.core.sync.performance

import com.alpha.gallery.core.sync.model.SyncResult
import com.alpha.gallery.core.sync.model.SyncType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Monitors and tracks sync performance metrics
 */
@Singleton
class SyncPerformanceMonitor @Inject constructor() {
    
    private val _performanceMetrics = MutableStateFlow(SyncPerformanceMetrics())
    val performanceMetrics: StateFlow<SyncPerformanceMetrics> = _performanceMetrics.asStateFlow()
    
    private val syncSessions = mutableMapOf<SyncType, SyncSession>()
    
    /**
     * Start tracking a sync operation
     */
    fun startSync(syncType: SyncType) {
        syncSessions[syncType] = SyncSession(
            syncType = syncType,
            startTime = System.currentTimeMillis()
        )
    }
    
    /**
     * End tracking a sync operation
     */
    fun endSync(syncType: SyncType, result: SyncResult?) {
        val session = syncSessions.remove(syncType) ?: return
        val endTime = System.currentTimeMillis()
        val duration = endTime - session.startTime
        
        val completedSession = session.copy(
            endTime = endTime,
            duration = duration,
            result = result
        )
        
        updateMetrics(completedSession)
    }
    
    /**
     * Record memory usage during sync
     */
    fun recordMemoryUsage() {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toFloat() / maxMemory) * 100
        
        val currentMetrics = _performanceMetrics.value
        _performanceMetrics.value = currentMetrics.copy(
            currentMemoryUsageMB = usedMemory / (1024 * 1024),
            peakMemoryUsageMB = maxOf(currentMetrics.peakMemoryUsageMB, usedMemory / (1024 * 1024)),
            memoryUsagePercent = memoryUsagePercent
        )
    }
    
    /**
     * Update performance metrics with completed session
     */
    private fun updateMetrics(session: SyncSession) {
        val currentMetrics = _performanceMetrics.value
        val isSuccess = session.result is SyncResult.Success
        
        val updatedMetrics = when (session.syncType) {
            SyncType.FULL -> currentMetrics.copy(
                totalFullSyncs = currentMetrics.totalFullSyncs + 1,
                successfulFullSyncs = if (isSuccess) currentMetrics.successfulFullSyncs + 1 else currentMetrics.successfulFullSyncs,
                averageFullSyncDuration = calculateNewAverage(
                    currentMetrics.averageFullSyncDuration,
                    session.duration,
                    currentMetrics.totalFullSyncs
                ),
                lastFullSyncDuration = session.duration,
                lastSyncTimestamp = session.endTime ?: System.currentTimeMillis()
            )
            
            SyncType.INCREMENTAL -> currentMetrics.copy(
                totalIncrementalSyncs = currentMetrics.totalIncrementalSyncs + 1,
                successfulIncrementalSyncs = if (isSuccess) currentMetrics.successfulIncrementalSyncs + 1 else currentMetrics.successfulIncrementalSyncs,
                averageIncrementalSyncDuration = calculateNewAverage(
                    currentMetrics.averageIncrementalSyncDuration,
                    session.duration,
                    currentMetrics.totalIncrementalSyncs
                ),
                lastIncrementalSyncDuration = session.duration,
                lastSyncTimestamp = session.endTime ?: System.currentTimeMillis()
            )
            
            else -> currentMetrics.copy(
                lastSyncTimestamp = session.endTime ?: System.currentTimeMillis()
            )
        }
        
        // Update items processed if successful
        if (session.result is SyncResult.Success) {
            _performanceMetrics.value = updatedMetrics.copy(
                totalItemsProcessed = currentMetrics.totalItemsProcessed + session.result.itemsProcessed,
                averageItemsPerSync = calculateNewAverage(
                    currentMetrics.averageItemsPerSync.toLong(),
                    session.result.itemsProcessed.toLong(),
                    currentMetrics.totalFullSyncs + currentMetrics.totalIncrementalSyncs
                ).toInt()
            )
        } else {
            _performanceMetrics.value = updatedMetrics
        }
    }
    
    /**
     * Calculate new running average
     */
    private fun calculateNewAverage(currentAverage: Long, newValue: Long, count: Int): Long {
        return if (count <= 1) {
            newValue
        } else {
            ((currentAverage * (count - 1)) + newValue) / count
        }
    }
    
    /**
     * Get sync success rate
     */
    fun getSyncSuccessRate(): Float {
        val metrics = _performanceMetrics.value
        val totalSyncs = metrics.totalFullSyncs + metrics.totalIncrementalSyncs
        val successfulSyncs = metrics.successfulFullSyncs + metrics.successfulIncrementalSyncs
        
        return if (totalSyncs > 0) {
            (successfulSyncs.toFloat() / totalSyncs) * 100
        } else {
            0f
        }
    }
    
    /**
     * Check if memory usage is critical
     */
    fun isMemoryUsageCritical(): Boolean {
        return _performanceMetrics.value.memoryUsagePercent > 85f
    }
    
    /**
     * Reset performance metrics
     */
    fun resetMetrics() {
        _performanceMetrics.value = SyncPerformanceMetrics()
    }
}

/**
 * Performance metrics for sync operations
 */
data class SyncPerformanceMetrics(
    val totalFullSyncs: Int = 0,
    val successfulFullSyncs: Int = 0,
    val totalIncrementalSyncs: Int = 0,
    val successfulIncrementalSyncs: Int = 0,
    val averageFullSyncDuration: Long = 0,
    val averageIncrementalSyncDuration: Long = 0,
    val lastFullSyncDuration: Long = 0,
    val lastIncrementalSyncDuration: Long = 0,
    val totalItemsProcessed: Int = 0,
    val averageItemsPerSync: Int = 0,
    val currentMemoryUsageMB: Long = 0,
    val peakMemoryUsageMB: Long = 0,
    val memoryUsagePercent: Float = 0f,
    val lastSyncTimestamp: Long = 0
) {
    val fullSyncSuccessRate: Float
        get() = if (totalFullSyncs > 0) (successfulFullSyncs.toFloat() / totalFullSyncs) * 100 else 0f
    
    val incrementalSyncSuccessRate: Float
        get() = if (totalIncrementalSyncs > 0) (successfulIncrementalSyncs.toFloat() / totalIncrementalSyncs) * 100 else 0f
}

/**
 * Represents a sync session for performance tracking
 */
private data class SyncSession(
    val syncType: SyncType,
    val startTime: Long,
    val endTime: Long? = null,
    val duration: Long = 0,
    val result: SyncResult? = null
)
