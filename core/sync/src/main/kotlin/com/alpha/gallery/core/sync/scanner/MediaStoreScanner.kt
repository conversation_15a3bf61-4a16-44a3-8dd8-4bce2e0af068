package com.alpha.gallery.core.sync.scanner

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import com.alpha.gallery.core.sync.model.MediaStoreItem
import com.alpha.gallery.core.sync.util.SyncConstants
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Efficient MediaStore scanner with pagination and batch processing
 */
@Singleton
class MediaStoreScanner @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val contentResolver: ContentResolver = context.contentResolver
    
    /**
     * Scan all media items with pagination
     */
    suspend fun scanAllMedia(
        batchSize: Int = SyncConstants.SYNC_BATCH_SIZE,
        onBatchProcessed: suspend (List<MediaStoreItem>) -> Unit = {}
    ): List<MediaStoreItem> = withContext(Dispatchers.IO) {
        val allItems = mutableListOf<MediaStoreItem>()
        var offset = 0
        
        do {
            val batch = scanMediaBatch(offset, batchSize)
            if (batch.isNotEmpty()) {
                allItems.addAll(batch)
                onBatchProcessed(batch)
                offset += batchSize
            }
        } while (batch.size == batchSize)
        
        allItems
    }
    
    /**
     * Scan media items modified after timestamp (incremental sync)
     */
    suspend fun scanMediaModifiedAfter(
        timestamp: Long,
        batchSize: Int = SyncConstants.SYNC_BATCH_SIZE
    ): List<MediaStoreItem> = withContext(Dispatchers.IO) {
        val items = mutableListOf<MediaStoreItem>()
        
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .modifiedAfter(timestamp)
            .orderByDateModifiedDesc()
            .limit(batchSize)
            .execute()?.use { cursor ->
                items.addAll(MediaStoreMapper.mapCursorToMediaStoreItems(cursor))
            }
        
        items
    }
    
    /**
     * Scan media items in batches as Flow for memory efficiency
     */
    fun scanMediaAsFlow(
        batchSize: Int = SyncConstants.SYNC_BATCH_SIZE
    ): Flow<List<MediaStoreItem>> = flow {
        var offset = 0
        
        do {
            val batch = scanMediaBatch(offset, batchSize)
            if (batch.isNotEmpty()) {
                emit(batch)
                offset += batchSize
            }
        } while (batch.size == batchSize)
    }.flowOn(Dispatchers.IO)
    
    /**
     * Get total media count for progress tracking
     */
    suspend fun getTotalMediaCount(): Int = withContext(Dispatchers.IO) {
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .execute()?.use { cursor ->
                cursor.count
            } ?: 0
    }
    
    /**
     * Scan specific media batch
     */
    private suspend fun scanMediaBatch(
        offset: Int,
        batchSize: Int
    ): List<MediaStoreItem> = withContext(Dispatchers.IO) {
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .orderByDateModifiedDesc()
            .limit(batchSize)
            .offset(offset)
            .execute()?.use { cursor ->
                MediaStoreMapper.mapCursorToMediaStoreItems(cursor)
            } ?: emptyList()
    }
    
    /**
     * Scan media items as sequence for memory-efficient processing
     */
    suspend fun scanMediaAsSequence(): Sequence<MediaStoreItem> = withContext(Dispatchers.IO) {
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .orderByDateModifiedDesc()
            .execute()?.use { cursor ->
                MediaStoreMapper.mapCursorToSequence(cursor)
            } ?: emptySequence()
    }
    
    /**
     * Check if media item exists in MediaStore
     */
    suspend fun mediaItemExists(mediaStoreId: Long): Boolean = withContext(Dispatchers.IO) {
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .where("${android.provider.MediaStore.MediaColumns._ID} = ?", arrayOf(mediaStoreId.toString()))
            .execute()?.use { cursor ->
                cursor.count > 0
            } ?: false
    }
    
    /**
     * Get media items by IDs for batch verification
     */
    suspend fun getMediaItemsByIds(ids: List<Long>): List<MediaStoreItem> = withContext(Dispatchers.IO) {
        if (ids.isEmpty()) return@withContext emptyList()
        
        val placeholders = ids.joinToString(",") { "?" }
        val selectionArgs = ids.map { it.toString() }.toTypedArray()
        
        MediaStoreQuery.builder(contentResolver)
            .allMedia()
            .where("${android.provider.MediaStore.MediaColumns._ID} IN ($placeholders)", selectionArgs)
            .execute()?.use { cursor ->
                MediaStoreMapper.mapCursorToMediaStoreItems(cursor)
            } ?: emptyList()
    }
}
