package com.alpha.gallery.core.sync.manager

import com.alpha.gallery.core.database.dao.MediaDao
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.entity.MediaEntity
import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.sync.data.MediaEntityMapper
import com.alpha.gallery.core.sync.model.MediaStoreItem
import com.alpha.gallery.core.sync.util.SyncConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Processes media items in batches for optimal performance
 */
@Singleton
class BatchProcessor @Inject constructor(
    private val mediaDao: MediaDao,
    private val albumDao: AlbumDao
) {
    
    /**
     * Process a batch of MediaStore items
     */
    suspend fun processBatch(
        mediaStoreItems: List<MediaStoreItem>,
        syncTimestamp: Long = System.currentTimeMillis()
    ): BatchResult = withContext(Dispatchers.IO) {
        
        val startTime = System.currentTimeMillis()
        var itemsAdded = 0
        var itemsUpdated = 0
        var albumsProcessed = 0
        
        try {
            // Convert MediaStore items to entities
            val mediaEntities = MediaEntityMapper.mapToMediaEntities(mediaStoreItems, syncTimestamp)
            
            // Get existing items by MediaStore IDs for conflict resolution
            val mediaStoreIds = mediaStoreItems.map { it.id }
            val existingItems = mediaDao.getMediaItemsByMediaStoreIds(mediaStoreIds)
            val existingItemsMap = existingItems.associateBy { it.mediaStoreId }
            
            // Separate new and updated items
            val (newItems, updatedItems) = mediaEntities.partition { entity ->
                existingItemsMap[entity.mediaStoreId] == null
            }
            
            // Process new items
            if (newItems.isNotEmpty()) {
                mediaDao.insertMediaItems(newItems)
                itemsAdded = newItems.size
            }
            
            // Process updated items (only if actually changed)
            val itemsToUpdate = updatedItems.filter { newEntity ->
                val existing = existingItemsMap[newEntity.mediaStoreId]
                existing != null && hasSignificantChanges(existing, newEntity)
            }
            
            if (itemsToUpdate.isNotEmpty()) {
                // Preserve database ID and other local fields
                val updatedEntities = itemsToUpdate.map { newEntity ->
                    val existing = existingItemsMap[newEntity.mediaStoreId]!!
                    newEntity.copy(
                        id = existing.id,
                        isFavorite = existing.isFavorite,
                        thumbnailPath = existing.thumbnailPath
                    )
                }
                mediaDao.updateMediaItems(updatedEntities)
                itemsUpdated = updatedEntities.size
            }
            
            // Process albums
            albumsProcessed = processAlbums(mediaStoreItems, syncTimestamp)
            
            val duration = System.currentTimeMillis() - startTime
            
            BatchResult.Success(
                itemsProcessed = mediaStoreItems.size,
                itemsAdded = itemsAdded,
                itemsUpdated = itemsUpdated,
                albumsProcessed = albumsProcessed,
                durationMs = duration
            )
            
        } catch (e: Exception) {
            BatchResult.Error(
                exception = e,
                itemsProcessed = 0
            )
        }
    }
    
    /**
     * Process albums from MediaStore items
     */
    private suspend fun processAlbums(
        mediaStoreItems: List<MediaStoreItem>,
        syncTimestamp: Long
    ): Int {
        val albums = MediaEntityMapper.extractAlbums(mediaStoreItems, syncTimestamp)
        if (albums.isEmpty()) return 0
        
        // Get existing albums
        val bucketIds = albums.map { it.bucketId }
        val existingAlbums = bucketIds.mapNotNull { bucketId ->
            albumDao.getAlbumByBucketId(bucketId)
        }.associateBy { it.bucketId }
        
        // Separate new and updated albums
        val (newAlbums, updatedAlbums) = albums.partition { album ->
            existingAlbums[album.bucketId] == null
        }
        
        // Insert new albums
        if (newAlbums.isNotEmpty()) {
            albumDao.insertAlbums(newAlbums)
        }
        
        // Update existing albums
        updatedAlbums.forEach { newAlbum ->
            val existing = existingAlbums[newAlbum.bucketId]
            if (existing != null) {
                val updatedAlbum = newAlbum.copy(
                    id = existing.id,
                    mediaCount = existing.mediaCount + newAlbum.mediaCount
                )
                albumDao.updateAlbum(updatedAlbum)
            }
        }
        
        return albums.size
    }
    
    /**
     * Check if there are significant changes between existing and new entity
     */
    private fun hasSignificantChanges(existing: MediaEntity, new: MediaEntity): Boolean {
        return existing.dateModified != new.dateModified ||
                existing.size != new.size ||
                existing.displayName != new.displayName ||
                existing.filePath != new.filePath ||
                existing.width != new.width ||
                existing.height != new.height ||
                existing.duration != new.duration
    }
    
    /**
     * Clean up orphaned items (items that no longer exist in MediaStore)
     */
    suspend fun cleanupOrphanedItems(
        validMediaStoreIds: Set<Long>,
        batchSize: Int = SyncConstants.SYNC_BATCH_SIZE
    ): Int = withContext(Dispatchers.IO) {
        var totalCleaned = 0
        var offset = 0
        
        do {
            val batch = mediaDao.getMediaItemsModifiedAfter(0, batchSize)
            val orphanedIds = batch
                .filter { !validMediaStoreIds.contains(it.mediaStoreId) }
                .map { it.mediaStoreId }
            
            if (orphanedIds.isNotEmpty()) {
                mediaDao.softDeleteMediaItemsByMediaStoreIds(orphanedIds)
                totalCleaned += orphanedIds.size
            }
            
            offset += batchSize
        } while (batch.size == batchSize)
        
        totalCleaned
    }
}

/**
 * Result of batch processing operation
 */
sealed class BatchResult {
    data class Success(
        val itemsProcessed: Int,
        val itemsAdded: Int,
        val itemsUpdated: Int,
        val albumsProcessed: Int,
        val durationMs: Long
    ) : BatchResult()
    
    data class Error(
        val exception: Throwable,
        val itemsProcessed: Int
    ) : BatchResult()
}
