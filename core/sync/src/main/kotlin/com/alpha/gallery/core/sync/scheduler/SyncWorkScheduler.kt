package com.alpha.gallery.core.sync.scheduler

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.work.*
import com.alpha.gallery.core.sync.model.SyncType
import com.alpha.gallery.core.sync.util.SyncConstants
import com.alpha.gallery.core.sync.worker.MediaSyncWorker
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Schedules and manages sync work requests
 */
@Singleton
class SyncWorkScheduler @Inject constructor(
    private val workManager: WorkManager
) {
    
    /**
     * Schedule periodic background sync
     */
    fun schedulePeriodicSync() {
        val periodicSyncRequest = MediaSyncWorker.createPeriodicSyncRequest()
        
        workManager.enqueueUniquePeriodicWork(
            SyncConstants.PERIODIC_SYNC_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            periodicSyncRequest
        )
    }
    
    /**
     * Schedule immediate one-time sync
     */
    fun scheduleImmediateSync(syncType: SyncType = SyncType.MANUAL) {
        Log.d(TAG, "scheduleImmediateSync: ")
        val immediateSyncRequest = MediaSyncWorker.createImmediateSyncRequest(syncType)
        
        workManager.enqueueUniqueWork(
            SyncConstants.IMMEDIATE_SYNC_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            immediateSyncRequest
        )
    }
    
    /**
     * Schedule sync triggered by ContentObserver
     */
    fun scheduleObserverTriggeredSync() {
        // Use a short delay to batch multiple rapid changes
        val observerSyncRequest = OneTimeWorkRequestBuilder<MediaSyncWorker>()
            .setInputData(
                workDataOf(MediaSyncWorker.KEY_SYNC_TYPE to SyncType.OBSERVER.name)
            )
            .setInitialDelay(5, java.util.concurrent.TimeUnit.SECONDS) // 5 second delay
            .setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                    .build()
            )
            .addTag("observer_sync")
            .build()
        
        workManager.enqueueUniqueWork(
            "observer_sync_work",
            ExistingWorkPolicy.REPLACE, // Replace to avoid duplicate syncs
            observerSyncRequest
        )
    }
    
    /**
     * Cancel all sync work
     */
    fun cancelAllSync() {
        workManager.cancelAllWorkByTag(SyncConstants.SYNC_WORK_NAME)
        workManager.cancelUniqueWork(SyncConstants.PERIODIC_SYNC_WORK_NAME)
        workManager.cancelUniqueWork(SyncConstants.IMMEDIATE_SYNC_WORK_NAME)
    }
    
    /**
     * Cancel periodic sync only
     */
    fun cancelPeriodicSync() {
        workManager.cancelUniqueWork(SyncConstants.PERIODIC_SYNC_WORK_NAME)
    }
    
    /**
     * Cancel immediate sync only
     */
    fun cancelImmediateSync() {
        workManager.cancelUniqueWork(SyncConstants.IMMEDIATE_SYNC_WORK_NAME)
    }
    
    /**
     * Get sync work status
     */
    fun getSyncWorkStatus(): LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData(SyncConstants.SYNC_WORK_NAME)
    }
    
    /**
     * Get periodic sync work status
     */
    fun getPeriodicSyncStatus(): LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkLiveData(SyncConstants.PERIODIC_SYNC_WORK_NAME)
    }
    
    /**
     * Check if sync is currently running
     */
    suspend fun isSyncRunning(): Boolean {
        val workInfos = workManager.getWorkInfosByTag(SyncConstants.SYNC_WORK_NAME).await()
        return workInfos.any { it.state == WorkInfo.State.RUNNING }
    }
    
    /**
     * Get last sync work info
     */
    suspend fun getLastSyncWorkInfo(): WorkInfo? {
        val workInfos = workManager.getWorkInfosByTag(SyncConstants.SYNC_WORK_NAME).await()
        return workInfos.maxByOrNull { it.id.hashCode() }
    }

    companion object {
        private const val TAG = "SyncWorkScheduler"
    }
}
