package com.alpha.gallery.core.sync.data

import com.alpha.gallery.core.database.entity.MediaEntity
import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.sync.model.MediaStoreItem

/**
 * Maps between MediaStore items and database entities
 */
object MediaEntityMapper {
    
    /**
     * Convert MediaStoreItem to MediaEntity
     */
    fun mapToMediaEntity(
        mediaStoreItem: MediaStoreItem,
        syncTimestamp: Long = System.currentTimeMillis()
    ): MediaEntity {
        return MediaEntity(
            mediaStoreId = mediaStoreItem.id,
            displayName = mediaStoreItem.displayName,
            filePath = mediaStoreItem.filePath,
            uri = mediaStoreItem.uri,
            mimeType = mediaStoreItem.mimeType,
            size = mediaStoreItem.size,
            dateAdded = mediaStoreItem.dateAdded,
            dateModified = mediaStoreItem.dateModified,
            width = mediaStoreItem.width,
            height = mediaStoreItem.height,
            duration = mediaStoreItem.duration,
            albumId = mediaStoreItem.bucketId,
            albumName = mediaStoreItem.bucketDisplayName,
            isVideo = mediaStoreItem.isVideo,
            syncTimestamp = syncTimestamp
        )
    }
    
    /**
     * Convert list of MediaStoreItems to MediaEntities
     */
    fun mapToMediaEntities(
        mediaStoreItems: List<MediaStoreItem>,
        syncTimestamp: Long = System.currentTimeMillis()
    ): List<MediaEntity> {
        return mediaStoreItems.map { mapToMediaEntity(it, syncTimestamp) }
    }
    
    /**
     * Create AlbumEntity from MediaStoreItem bucket information
     */
    fun mapToAlbumEntity(
        mediaStoreItem: MediaStoreItem,
        mediaCount: Int = 1,
        coverImagePath: String? = null,
        syncTimestamp: Long = System.currentTimeMillis()
    ): AlbumEntity? {
        val bucketId = mediaStoreItem.bucketId ?: return null
        val bucketName = mediaStoreItem.bucketDisplayName ?: "Unknown Album"
        
        // Extract path from file path (remove filename)
        val albumPath = mediaStoreItem.filePath.substringBeforeLast("/")
        
        return AlbumEntity(
            bucketId = bucketId,
            name = bucketName,
            path = albumPath,
            coverImagePath = coverImagePath,
            mediaCount = mediaCount,
            dateAdded = mediaStoreItem.dateAdded,
            dateModified = mediaStoreItem.dateModified,
            syncTimestamp = syncTimestamp
        )
    }
    
    /**
     * Extract unique albums from MediaStore items
     */
    fun extractAlbums(
        mediaStoreItems: List<MediaStoreItem>,
        syncTimestamp: Long = System.currentTimeMillis()
    ): List<AlbumEntity> {
        val albumMap = mutableMapOf<String, AlbumInfo>()
        
        // Group items by bucket ID and collect album info
        mediaStoreItems.forEach { item ->
            val bucketId = item.bucketId
            if (bucketId != null) {
                val existing = albumMap[bucketId]
                if (existing == null) {
                    albumMap[bucketId] = AlbumInfo(
                        item = item,
                        count = 1,
                        latestDate = item.dateModified,
                        coverImagePath = if (!item.isVideo) item.filePath else null
                    )
                } else {
                    albumMap[bucketId] = existing.copy(
                        count = existing.count + 1,
                        latestDate = maxOf(existing.latestDate, item.dateModified),
                        coverImagePath = existing.coverImagePath ?: if (!item.isVideo) item.filePath else null
                    )
                }
            }
        }
        
        // Convert to AlbumEntity list
        return albumMap.values.mapNotNull { albumInfo ->
            mapToAlbumEntity(
                mediaStoreItem = albumInfo.item.copy(dateModified = albumInfo.latestDate),
                mediaCount = albumInfo.count,
                coverImagePath = albumInfo.coverImagePath,
                syncTimestamp = syncTimestamp
            )
        }
    }
    
    /**
     * Helper data class for album information aggregation
     */
    private data class AlbumInfo(
        val item: MediaStoreItem,
        val count: Int,
        val latestDate: Long,
        val coverImagePath: String?
    )
}
