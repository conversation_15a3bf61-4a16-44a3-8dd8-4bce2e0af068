package com.alpha.gallery.core.sync.manager

import com.alpha.gallery.core.database.dao.SyncMetadataDao
import com.alpha.gallery.core.database.entity.SyncMetadataEntity
import com.alpha.gallery.core.sync.scanner.MediaStoreScanner
import com.alpha.gallery.core.sync.model.SyncType
import com.alpha.gallery.core.sync.util.SyncConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implements incremental sync strategy using timestamps
 */
@Singleton
class IncrementalSyncStrategy @Inject constructor(
    private val mediaStoreScanner: MediaStoreScanner,
    private val syncMetadataDao: SyncMetadataDao,
    private val batchProcessor: BatchProcessor
) {
    
    /**
     * Perform incremental sync based on last sync timestamp
     */
    suspend fun performIncrementalSync(): IncrementalSyncResult = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        
        try {
            // Get last sync timestamp
            val lastSyncTimestamp = getLastSyncTimestamp()
            
            // If no previous sync, perform full sync
            if (lastSyncTimestamp == null) {
                return@withContext IncrementalSyncResult.RequiresFullSync
            }
            
            // Scan items modified since last sync
            val modifiedItems = mediaStoreScanner.scanMediaModifiedAfter(
                timestamp = lastSyncTimestamp,
                batchSize = SyncConstants.SYNC_BATCH_SIZE
            )
            
            if (modifiedItems.isEmpty()) {
                // Update last sync timestamp even if no changes
                updateLastSyncTimestamp(startTime)
                return@withContext IncrementalSyncResult.Success(
                    itemsProcessed = 0,
                    itemsAdded = 0,
                    itemsUpdated = 0,
                    durationMs = System.currentTimeMillis() - startTime
                )
            }
            
            // Process modified items in batches
            var totalProcessed = 0
            var totalAdded = 0
            var totalUpdated = 0
            
            modifiedItems.chunked(SyncConstants.SYNC_BATCH_SIZE).forEach { batch ->
                when (val result = batchProcessor.processBatch(batch, startTime)) {
                    is BatchResult.Success -> {
                        totalProcessed += result.itemsProcessed
                        totalAdded += result.itemsAdded
                        totalUpdated += result.itemsUpdated
                    }
                    is BatchResult.Error -> {
                        return@withContext IncrementalSyncResult.Error(
                            exception = result.exception,
                            itemsProcessed = totalProcessed
                        )
                    }
                }
            }
            
            // Update last sync timestamp
            updateLastSyncTimestamp(startTime)
            
            IncrementalSyncResult.Success(
                itemsProcessed = totalProcessed,
                itemsAdded = totalAdded,
                itemsUpdated = totalUpdated,
                durationMs = System.currentTimeMillis() - startTime
            )
            
        } catch (e: Exception) {
            IncrementalSyncResult.Error(
                exception = e,
                itemsProcessed = 0
            )
        }
    }
    
    /**
     * Check if incremental sync is possible
     */
    suspend fun canPerformIncrementalSync(): Boolean = withContext(Dispatchers.IO) {
        val lastSyncTimestamp = getLastSyncTimestamp()
        val syncVersion = getSyncVersion()
        
        // Can perform incremental sync if:
        // 1. We have a last sync timestamp
        // 2. Sync version matches current version
        // 3. Last sync was not too long ago (to prevent huge incremental syncs)
        lastSyncTimestamp != null && 
        syncVersion == SyncConstants.CURRENT_SYNC_VERSION &&
        (System.currentTimeMillis() - lastSyncTimestamp) < MAX_INCREMENTAL_SYNC_AGE_MS
    }
    
    /**
     * Get last sync timestamp
     */
    private suspend fun getLastSyncTimestamp(): Long? {
        return syncMetadataDao.getSyncMetadataValue(SyncConstants.LAST_SYNC_TIMESTAMP_KEY)?.toLongOrNull()
    }
    
    /**
     * Update last sync timestamp
     */
    private suspend fun updateLastSyncTimestamp(timestamp: Long) {
        syncMetadataDao.insertSyncMetadata(
            SyncMetadataEntity(
                key = SyncConstants.LAST_SYNC_TIMESTAMP_KEY,
                value = timestamp.toString(),
                timestamp = timestamp,
                syncType = SyncType.INCREMENTAL.name
            )
        )
    }
    
    /**
     * Get sync version
     */
    private suspend fun getSyncVersion(): Int {
        return syncMetadataDao.getSyncMetadataValue(SyncConstants.SYNC_VERSION_KEY)?.toIntOrNull() 
            ?: 0
    }
    
    /**
     * Update sync version
     */
    suspend fun updateSyncVersion() {
        syncMetadataDao.insertSyncMetadata(
            SyncMetadataEntity(
                key = SyncConstants.SYNC_VERSION_KEY,
                value = SyncConstants.CURRENT_SYNC_VERSION.toString(),
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * Reset incremental sync state (force full sync next time)
     */
    suspend fun resetIncrementalSyncState() {
        syncMetadataDao.deleteSyncMetadata(SyncConstants.LAST_SYNC_TIMESTAMP_KEY)
    }
    
    companion object {
        // Maximum age for incremental sync (7 days)
        private const val MAX_INCREMENTAL_SYNC_AGE_MS = 7 * 24 * 60 * 60 * 1000L
    }
}

/**
 * Result of incremental sync operation
 */
sealed class IncrementalSyncResult {
    data class Success(
        val itemsProcessed: Int,
        val itemsAdded: Int,
        val itemsUpdated: Int,
        val durationMs: Long
    ) : IncrementalSyncResult()
    
    data class Error(
        val exception: Throwable,
        val itemsProcessed: Int
    ) : IncrementalSyncResult()
    
    object RequiresFullSync : IncrementalSyncResult()
}
