package com.alpha.gallery.core.sync.scanner

import android.content.ContentResolver
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import com.alpha.gallery.core.sync.util.SyncConstants

/**
 * Builder for MediaStore queries with optimized performance
 */
class MediaStoreQuery private constructor(
    private val contentResolver: ContentResolver
) {
    
    private var uri: Uri = SyncConstants.EXTERNAL_CONTENT_URI
    private var projection: Array<String> = SyncConstants.MEDIA_PROJECTION
    private var selection: String? = null
    private var selectionArgs: Array<String>? = null
    private var sortOrder: String? = null
    private var limit: Int? = null
    private var offset: Int = 0
    
    companion object {
        fun builder(contentResolver: ContentResolver) = MediaStoreQuery(contentResolver)
        
        /**
         * Default selection for images and videos only
         */
        private const val MEDIA_TYPE_SELECTION = "${MediaStore.Files.FileColumns.MEDIA_TYPE} = ? OR ${MediaStore.Files.FileColumns.MEDIA_TYPE} = ?"
        private val MEDIA_TYPE_ARGS = arrayOf(
            MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE.toString(),
            MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO.toString()
        )
        
        /**
         * Selection for incremental sync (items modified after timestamp)
         */
        private const val INCREMENTAL_SELECTION = "$MEDIA_TYPE_SELECTION AND ${MediaStore.MediaColumns.DATE_MODIFIED} > ?"
    }
    
    /**
     * Query only images
     */
    fun imagesOnly(): MediaStoreQuery {
        uri = SyncConstants.IMAGES_URI
        return this
    }
    
    /**
     * Query only videos
     */
    fun videosOnly(): MediaStoreQuery {
        uri = SyncConstants.VIDEOS_URI
        return this
    }
    
    /**
     * Query both images and videos (default)
     */
    fun allMedia(): MediaStoreQuery {
        uri = SyncConstants.EXTERNAL_CONTENT_URI
        selection = MEDIA_TYPE_SELECTION
        selectionArgs = MEDIA_TYPE_ARGS
        return this
    }
    
    /**
     * Add incremental sync filter (items modified after timestamp)
     */
    fun modifiedAfter(timestamp: Long): MediaStoreQuery {
        selection = INCREMENTAL_SELECTION
        selectionArgs = MEDIA_TYPE_ARGS + timestamp.toString()
        return this
    }
    
    /**
     * Add custom selection
     */
    fun where(selection: String, selectionArgs: Array<String>? = null): MediaStoreQuery {
        this.selection = if (this.selection != null) {
            "${this.selection} AND ($selection)"
        } else {
            selection
        }
        
        this.selectionArgs = if (this.selectionArgs != null && selectionArgs != null) {
            this.selectionArgs!! + selectionArgs
        } else {
            selectionArgs ?: this.selectionArgs
        }
        return this
    }
    
    /**
     * Set sort order
     */
    fun orderBy(sortOrder: String): MediaStoreQuery {
        this.sortOrder = sortOrder
        return this
    }
    
    /**
     * Order by date modified (newest first)
     */
    fun orderByDateModifiedDesc(): MediaStoreQuery {
        return orderBy("${MediaStore.MediaColumns.DATE_MODIFIED} DESC")
    }
    
    /**
     * Order by date added (newest first)
     */
    fun orderByDateAddedDesc(): MediaStoreQuery {
        return orderBy("${MediaStore.MediaColumns.DATE_ADDED} DESC")
    }
    
    /**
     * Set pagination limit
     */
    fun limit(limit: Int): MediaStoreQuery {
        this.limit = limit
        return this
    }
    
    /**
     * Set pagination offset
     */
    fun offset(offset: Int): MediaStoreQuery {
        this.offset = offset
        return this
    }
    
    /**
     * Execute query and return cursor
     */
    fun execute(): Cursor? {
        val finalSortOrder = buildSortOrder()
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Use bundle for better performance on API 26+
            val queryArgs = android.os.Bundle().apply {
                selection?.let { putString(ContentResolver.QUERY_ARG_SQL_SELECTION, it) }
                selectionArgs?.let { putStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS, it) }
                finalSortOrder?.let { putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, it) }
                limit?.let { 
                    putInt(ContentResolver.QUERY_ARG_LIMIT, it)
                    putInt(ContentResolver.QUERY_ARG_OFFSET, offset)
                }
            }
            contentResolver.query(uri, projection, queryArgs, null)
        } else {
            contentResolver.query(uri, projection, selection, selectionArgs, finalSortOrder)
        }
    }
    
    /**
     * Build final sort order with limit for older Android versions
     */
    private fun buildSortOrder(): String? {
        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O && limit != null) {
            val baseOrder = sortOrder ?: "${MediaStore.MediaColumns.DATE_MODIFIED} DESC"
            "$baseOrder LIMIT $limit OFFSET $offset"
        } else {
            sortOrder
        }
    }
}
