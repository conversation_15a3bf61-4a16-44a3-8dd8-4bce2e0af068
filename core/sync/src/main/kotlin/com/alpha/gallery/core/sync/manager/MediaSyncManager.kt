package com.alpha.gallery.core.sync.manager

import com.alpha.gallery.core.database.dao.SyncMetadataDao
import com.alpha.gallery.core.database.entity.SyncMetadataEntity
import com.alpha.gallery.core.sync.scanner.MediaStoreScanner
import com.alpha.gallery.core.sync.model.*
import com.alpha.gallery.core.sync.util.SyncConstants
import com.alpha.gallery.core.sync.error.SyncErrorHandler
import com.alpha.gallery.core.sync.performance.SyncPerformanceMonitor
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Main orchestrator for media synchronization operations
 */
@Singleton
class MediaSyncManager @Inject constructor(
    private val mediaStoreScanner: MediaStoreScanner,
    private val batchProcessor: BatchProcessor,
    private val incrementalSyncStrategy: IncrementalSyncStrategy,
    private val syncMetadataDao: SyncMetadataDao,
    private val syncErrorHandler: SyncError<PERSON>andler,
    private val performanceMonitor: SyncPerformanceMonitor
) {
    
    private val _syncStatus = MutableStateFlow(SyncStatus())
    val syncStatus: StateFlow<SyncStatus> = _syncStatus.asStateFlow()
    
    private var currentSyncJob: Job? = null
    
    /**
     * Perform full synchronization
     */
    suspend fun performFullSync(): SyncResult = withContext(Dispatchers.IO) {
        if (_syncStatus.value.isRunning) {
            return@withContext SyncResult.Error(
                exception = IllegalStateException("Sync already in progress"),
                syncType = SyncType.FULL
            )
        }
        
        performanceMonitor.startSync(SyncType.FULL)
        updateSyncStatus(isRunning = true, currentType = SyncType.FULL)
        
        try {
            val startTime = System.currentTimeMillis()
            
            // Get total count for progress tracking
            val totalCount = mediaStoreScanner.getTotalMediaCount()
            updateSyncProgress(SyncProgress(
                totalItems = totalCount,
                processedItems = 0,
                currentBatch = 0,
                totalBatches = (totalCount + SyncConstants.SYNC_BATCH_SIZE - 1) / SyncConstants.SYNC_BATCH_SIZE,
                currentPhase = SyncPhase.SCANNING_MEDIASTORE
            ))
            
            var totalProcessed = 0
            var totalAdded = 0
            var totalUpdated = 0
            var currentBatch = 0
            
            // Process media in batches
            var errorResult: SyncResult.Error? = null
            mediaStoreScanner.scanMediaAsFlow(SyncConstants.SYNC_BATCH_SIZE)
                .collect { batch ->
                    updateSyncProgress(SyncProgress(
                        totalItems = totalCount,
                        processedItems = totalProcessed,
                        currentBatch = currentBatch,
                        totalBatches = (totalCount + SyncConstants.SYNC_BATCH_SIZE - 1) / SyncConstants.SYNC_BATCH_SIZE,
                        currentPhase = SyncPhase.PROCESSING_BATCH
                    ))

                    when (val result = batchProcessor.processBatch(batch, startTime)) {
                        is BatchResult.Success -> {
                            totalProcessed += result.itemsProcessed
                            totalAdded += result.itemsAdded
                            totalUpdated += result.itemsUpdated
                            currentBatch++
                        }
                        is BatchResult.Error -> {
                            errorResult = SyncResult.Error(
                                exception = result.exception,
                                itemsProcessed = totalProcessed,
                                syncType = SyncType.FULL
                            )
                            return@collect // Exit the collect loop
                        }
                    }
                }

            // Check if there was an error during processing
            errorResult?.let { syncResult ->
                updateSyncStatus(isRunning = false, lastSyncResult = syncResult)
                performanceMonitor.endSync(SyncType.FULL, syncResult)
                return@withContext syncResult
            }
            
            // Update sync metadata
            updateSyncMetadata(startTime, SyncType.FULL)
            
            val syncResult = SyncResult.Success(
                itemsProcessed = totalProcessed,
                itemsAdded = totalAdded,
                itemsUpdated = totalUpdated,
                itemsDeleted = 0,
                durationMs = System.currentTimeMillis() - startTime,
                syncType = SyncType.FULL
            )
            
            updateSyncStatus(
                isRunning = false,
                lastSyncTimestamp = startTime,
                lastSyncResult = syncResult
            )
            
            performanceMonitor.endSync(SyncType.FULL, syncResult)
            syncResult
            
        } catch (e: Exception) {
            val syncResult = syncErrorHandler.handleSyncError(e, SyncType.FULL)
            updateSyncStatus(isRunning = false, lastSyncResult = syncResult)
            performanceMonitor.endSync(SyncType.FULL, syncResult)
            syncResult
        }
    }
    
    /**
     * Perform incremental synchronization
     */
    suspend fun performIncrementalSync(): SyncResult = withContext(Dispatchers.IO) {
        if (_syncStatus.value.isRunning) {
            return@withContext SyncResult.Error(
                exception = IllegalStateException("Sync already in progress"),
                syncType = SyncType.INCREMENTAL
            )
        }
        
        performanceMonitor.startSync(SyncType.INCREMENTAL)
        updateSyncStatus(isRunning = true, currentType = SyncType.INCREMENTAL)
        
        try {
            val startTime = System.currentTimeMillis()
            
            when (val result = incrementalSyncStrategy.performIncrementalSync()) {
                is IncrementalSyncResult.Success -> {
                    val syncResult = SyncResult.Success(
                        itemsProcessed = result.itemsProcessed,
                        itemsAdded = result.itemsAdded,
                        itemsUpdated = result.itemsUpdated,
                        itemsDeleted = 0,
                        durationMs = result.durationMs,
                        syncType = SyncType.INCREMENTAL
                    )
                    
                    updateSyncStatus(
                        isRunning = false,
                        lastSyncTimestamp = startTime,
                        lastSyncResult = syncResult
                    )
                    
                    performanceMonitor.endSync(SyncType.INCREMENTAL, syncResult)
                    syncResult
                }
                
                is IncrementalSyncResult.Error -> {
                    val syncResult = SyncResult.Error(
                        exception = result.exception,
                        itemsProcessed = result.itemsProcessed,
                        syncType = SyncType.INCREMENTAL
                    )
                    
                    updateSyncStatus(isRunning = false, lastSyncResult = syncResult)
                    performanceMonitor.endSync(SyncType.INCREMENTAL, syncResult)
                    syncResult
                }
                
                IncrementalSyncResult.RequiresFullSync -> {
                    updateSyncStatus(isRunning = false)
                    performanceMonitor.endSync(SyncType.INCREMENTAL, null)
                    // Automatically trigger full sync
                    return@withContext performFullSync()
                }
            }
            
        } catch (e: Exception) {
            val syncResult = syncErrorHandler.handleSyncError(e, SyncType.INCREMENTAL)
            updateSyncStatus(isRunning = false, lastSyncResult = syncResult)
            performanceMonitor.endSync(SyncType.INCREMENTAL, syncResult)
            syncResult
        }
    }
    
    /**
     * Perform smart sync (incremental if possible, full otherwise)
     */
    suspend fun performSmartSync(): SyncResult {
        return if (incrementalSyncStrategy.canPerformIncrementalSync()) {
            performIncrementalSync()
        } else {
            performFullSync()
        }
    }
    
    /**
     * Cancel current sync operation
     */
    fun cancelSync(reason: String = "User cancelled") {
        currentSyncJob?.cancel()
        updateSyncStatus(
            isRunning = false,
            lastSyncResult = SyncResult.Cancelled(
                reason = reason,
                itemsProcessed = _syncStatus.value.progress?.processedItems ?: 0,
                syncType = _syncStatus.value.currentType ?: SyncType.MANUAL
            )
        )
    }
    
    /**
     * Update sync status
     */
    private fun updateSyncStatus(
        isRunning: Boolean = _syncStatus.value.isRunning,
        currentType: SyncType? = _syncStatus.value.currentType,
        progress: SyncProgress? = _syncStatus.value.progress,
        lastSyncTimestamp: Long? = _syncStatus.value.lastSyncTimestamp,
        lastSyncResult: SyncResult? = _syncStatus.value.lastSyncResult
    ) {
        _syncStatus.value = SyncStatus(
            isRunning = isRunning,
            currentType = if (isRunning) currentType else null,
            progress = if (isRunning) progress else null,
            lastSyncTimestamp = lastSyncTimestamp,
            lastSyncResult = lastSyncResult
        )
    }
    
    /**
     * Update sync progress
     */
    private fun updateSyncProgress(progress: SyncProgress) {
        updateSyncStatus(progress = progress)
    }
    
    /**
     * Update sync metadata in database
     */
    private suspend fun updateSyncMetadata(timestamp: Long, syncType: SyncType) {
        syncMetadataDao.insertSyncMetadata(
            listOf(
                SyncMetadataEntity(
                    key = SyncConstants.LAST_SYNC_TIMESTAMP_KEY,
                    value = timestamp.toString(),
                    timestamp = timestamp,
                    syncType = syncType.name
                ),
                SyncMetadataEntity(
                    key = SyncConstants.SYNC_VERSION_KEY,
                    value = SyncConstants.CURRENT_SYNC_VERSION.toString(),
                    timestamp = timestamp
                )
            )
        )
    }
}
