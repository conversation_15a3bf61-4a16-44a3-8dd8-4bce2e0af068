package com.alpha.gallery.core.sync.scanner

import android.database.Cursor
import android.provider.MediaStore
import com.alpha.gallery.core.sync.model.MediaStoreItem

/**
 * Maps MediaStore cursor data to MediaStoreItem objects
 */
object MediaStoreMapper {
    
    /**
     * Map cursor to MediaStoreItem
     */
    fun mapCursorToMediaStoreItem(cursor: Cursor): MediaStoreItem? {
        return try {
            val idIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID)
            val displayNameIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME)
            val dataIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA)
            val mimeTypeIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.MIME_TYPE)
            val sizeIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE)
            val dateAddedIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATE_ADDED)
            val dateModifiedIndex = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATE_MODIFIED)
            val widthIndex = cursor.getColumnIndex(MediaStore.MediaColumns.WIDTH)
            val heightIndex = cursor.getColumnIndex(MediaStore.MediaColumns.HEIGHT)
            val durationIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DURATION)
            val bucketIdIndex = cursor.getColumnIndex(MediaStore.MediaColumns.BUCKET_ID)
            val bucketDisplayNameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.BUCKET_DISPLAY_NAME)
            
            MediaStoreItem(
                id = cursor.getLong(idIndex),
                displayName = cursor.getString(displayNameIndex) ?: "",
                filePath = cursor.getString(dataIndex) ?: "",
                mimeType = cursor.getString(mimeTypeIndex) ?: "",
                size = cursor.getLong(sizeIndex),
                dateAdded = cursor.getLong(dateAddedIndex) * 1000, // Convert to milliseconds
                dateModified = cursor.getLong(dateModifiedIndex) * 1000, // Convert to milliseconds
                width = if (widthIndex >= 0) cursor.getInt(widthIndex) else 0,
                height = if (heightIndex >= 0) cursor.getInt(heightIndex) else 0,
                duration = if (durationIndex >= 0) cursor.getLong(durationIndex) else 0,
                bucketId = if (bucketIdIndex >= 0) cursor.getString(bucketIdIndex) else null,
                bucketDisplayName = if (bucketDisplayNameIndex >= 0) cursor.getString(bucketDisplayNameIndex) else null
            )
        } catch (e: Exception) {
            // Log error but don't crash - skip this item
            null
        }
    }
    
    /**
     * Map cursor to list of MediaStoreItems
     */
    fun mapCursorToMediaStoreItems(cursor: Cursor): List<MediaStoreItem> {
        val items = mutableListOf<MediaStoreItem>()
        
        if (cursor.moveToFirst()) {
            do {
                mapCursorToMediaStoreItem(cursor)?.let { item ->
                    items.add(item)
                }
            } while (cursor.moveToNext())
        }
        
        return items
    }
    
    /**
     * Map cursor to sequence for memory-efficient processing
     */
    fun mapCursorToSequence(cursor: Cursor): Sequence<MediaStoreItem> {
        return sequence {
            if (cursor.moveToFirst()) {
                do {
                    mapCursorToMediaStoreItem(cursor)?.let { item ->
                        yield(item)
                    }
                } while (cursor.moveToNext())
            }
        }
    }
}
