package com.alpha.gallery.core.sync.model

/**
 * Data model representing a media item from MediaStore query
 */
data class MediaStoreItem(
    val id: Long,
    val displayName: String,
    val filePath: String,
    val mimeType: String,
    val size: Long,
    val dateAdded: Long,
    val dateModified: Long,
    val width: Int = 0,
    val height: Int = 0,
    val duration: Long = 0, // For videos, in milliseconds
    val bucketId: String? = null,
    val bucketDisplayName: String? = null
) {
    val isVideo: Boolean
        get() = mimeType.startsWith("video/")
    
    val isImage: Boolean
        get() = mimeType.startsWith("image/")
    
    val uri: String
        get() = if (isVideo) {
            "content://media/external/video/media/$id"
        } else {
            "content://media/external/images/media/$id"
        }
}
