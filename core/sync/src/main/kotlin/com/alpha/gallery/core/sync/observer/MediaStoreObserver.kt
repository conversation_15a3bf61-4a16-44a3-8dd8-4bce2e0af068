package com.alpha.gallery.core.sync.observer

import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import com.alpha.gallery.core.sync.permission.PermissionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Observes MediaStore changes and triggers sync operations
 */
@Singleton
class MediaStoreObserver @Inject constructor(
    @ApplicationContext private val context: Context,
    private val syncWorkScheduler: SyncWorkScheduler,
    private val permissionManager: PermissionManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var isObserving = false
    private var lastChangeTime = 0L
    private val changeDebounceMs = 2000L // 2 seconds debounce
    
    private val contentObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            handleMediaStoreChange(uri)
        }
        
        override fun onChange(selfChange: Boolean) {
            handleMediaStoreChange(null)
        }
    }
    
    /**
     * Start observing MediaStore changes
     */
    fun startObserving() {
        if (isObserving || !permissionManager.hasMediaPermissions()) {
            return
        }
        
        try {
            val contentResolver = context.contentResolver
            
            // Register observers for different media types
            contentResolver.registerContentObserver(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                true,
                contentObserver
            )
            
            contentResolver.registerContentObserver(
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                true,
                contentObserver
            )
            
            // Also observe the general Files URI for broader coverage
            contentResolver.registerContentObserver(
                MediaStore.Files.getContentUri("external"),
                true,
                contentObserver
            )
            
            isObserving = true
            println("MediaStore observer started")
            
        } catch (e: Exception) {
            println("Failed to start MediaStore observer: ${e.message}")
        }
    }
    
    /**
     * Stop observing MediaStore changes
     */
    fun stopObserving() {
        if (!isObserving) {
            return
        }
        
        try {
            context.contentResolver.unregisterContentObserver(contentObserver)
            isObserving = false
            println("MediaStore observer stopped")
        } catch (e: Exception) {
            println("Failed to stop MediaStore observer: ${e.message}")
        }
    }
    
    /**
     * Handle MediaStore change with debouncing
     */
    private fun handleMediaStoreChange(uri: Uri?) {
        val currentTime = System.currentTimeMillis()
        
        // Debounce rapid changes
        if (currentTime - lastChangeTime < changeDebounceMs) {
            return
        }
        
        lastChangeTime = currentTime
        
        scope.launch {
            try {
                // Log the change for debugging
                println("MediaStore change detected: $uri")
                
                // Determine if this is a significant change that requires sync
                if (isSignificantChange(uri)) {
                    // Schedule observer-triggered sync with a small delay
                    // to allow for batch processing of multiple changes
                    delay(1000) // 1 second delay
                    
                    if (permissionManager.hasMediaPermissions()) {
                        syncWorkScheduler.scheduleObserverTriggeredSync()
                        println("Observer-triggered sync scheduled")
                    }
                }
                
            } catch (e: Exception) {
                println("Error handling MediaStore change: ${e.message}")
            }
        }
    }
    
    /**
     * Determine if the change is significant enough to trigger sync
     */
    private fun isSignificantChange(uri: Uri?): Boolean {
        if (uri == null) return true
        
        val uriString = uri.toString()
        
        // Check if it's a media-related change
        return uriString.contains("images") || 
               uriString.contains("video") || 
               uriString.contains("media") ||
               uriString.contains("files")
    }
    
    /**
     * Check if observer is currently active
     */
    fun isObserving(): Boolean = isObserving
    
    /**
     * Restart observer (useful after permission changes)
     */
    fun restartObserver() {
        stopObserving()
        startObserving()
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        stopObserving()
        scope.cancel()
    }
}
