package com.alpha.gallery.core.sync.model

/**
 * Result of a sync operation
 */
sealed class SyncResult {
    
    /**
     * Sync completed successfully
     */
    data class Success(
        val itemsProcessed: Int,
        val itemsAdded: Int,
        val itemsUpdated: Int,
        val itemsDeleted: Int,
        val durationMs: Long,
        val syncType: SyncType
    ) : SyncResult()
    
    /**
     * Sync failed with error
     */
    data class Error(
        val exception: Throwable,
        val itemsProcessed: Int = 0,
        val syncType: SyncType
    ) : SyncResult()
    
    /**
     * Sync was cancelled
     */
    data class Cancelled(
        val reason: String,
        val itemsProcessed: Int = 0,
        val syncType: SyncType
    ) : SyncResult()
}

/**
 * Type of sync operation
 */
enum class SyncType {
    FULL,           // Complete sync of all media
    INCREMENTAL,    // Sync only changed items since last sync
    MANUAL,         // User-triggered sync
    PERIODIC,       // Scheduled background sync
    OBSERVER        // Triggered by ContentObserver
}

/**
 * Status of ongoing sync operation
 */
data class SyncStatus(
    val isRunning: Boolean = false,
    val currentType: SyncType? = null,
    val progress: SyncProgress? = null,
    val lastSyncTimestamp: Long? = null,
    val lastSyncResult: SyncResult? = null
)

/**
 * Progress information for sync operation
 */
data class SyncProgress(
    val totalItems: Int,
    val processedItems: Int,
    val currentBatch: Int,
    val totalBatches: Int,
    val currentPhase: SyncPhase
) {
    val progressPercentage: Float
        get() = if (totalItems > 0) (processedItems.toFloat() / totalItems) * 100f else 0f
}

/**
 * Current phase of sync operation
 */
enum class SyncPhase {
    INITIALIZING,
    SCANNING_MEDIASTORE,
    PROCESSING_BATCH,
    UPDATING_DATABASE,
    CLEANING_UP,
    COMPLETED
}
