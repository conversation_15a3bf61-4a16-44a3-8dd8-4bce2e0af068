package com.alpha.gallery.core.sync.worker

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.alpha.gallery.core.sync.manager.MediaSyncManager
import com.alpha.gallery.core.sync.model.SyncResult
import com.alpha.gallery.core.sync.model.SyncType
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.sync.util.SyncConstants
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import dagger.assisted.AssistedFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * WorkManager worker for background media synchronization
 */
@HiltWorker
class MediaSyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val mediaSyncManager: MediaSyncManager,
    private val permissionManager: PermissionManager
) : CoroutineWorker(context, workerParams) {

    /**
     * Factory interface for creating MediaSyncWorker instances with Hilt
     */
    @AssistedFactory
    interface Factory {
        fun create(context: Context, workerParams: WorkerParameters): MediaSyncWorker
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        Log.d(TAG, "doWork: Starting media sync worker...")
        
        // Check if we have required permissions
        if (!permissionManager.hasMediaPermissions()) {
            return@withContext Result.failure(
                workDataOf(
                    KEY_ERROR_MESSAGE to "Missing media permissions",
                    KEY_ERROR_TYPE to "PERMISSION_DENIED"
                )
            )
        }
        
        try {
            // Determine sync type from input data
            val syncTypeString = inputData.getString(KEY_SYNC_TYPE) ?: SyncType.INCREMENTAL.name
            val syncType = try {
                SyncType.valueOf(syncTypeString)
            } catch (e: IllegalArgumentException) {
                SyncType.INCREMENTAL
            }
            
            // Perform sync based on type
            val syncResult = when (syncType) {
                SyncType.FULL -> mediaSyncManager.performFullSync()
                SyncType.INCREMENTAL -> mediaSyncManager.performIncrementalSync()
                SyncType.MANUAL -> mediaSyncManager.performSmartSync()
                SyncType.PERIODIC -> mediaSyncManager.performIncrementalSync()
                SyncType.OBSERVER -> mediaSyncManager.performIncrementalSync()
            }
            
            // Return result based on sync outcome
            when (syncResult) {
                is SyncResult.Success -> {
                    Result.success(
                        workDataOf(
                            KEY_ITEMS_PROCESSED to syncResult.itemsProcessed,
                            KEY_ITEMS_ADDED to syncResult.itemsAdded,
                            KEY_ITEMS_UPDATED to syncResult.itemsUpdated,
                            KEY_ITEMS_DELETED to syncResult.itemsDeleted,
                            KEY_DURATION_MS to syncResult.durationMs,
                            KEY_SYNC_TYPE to syncResult.syncType.name
                        )
                    )
                }
                
                is SyncResult.Error -> {
                    Result.retry()
                }
                
                is SyncResult.Cancelled -> {
                    Result.failure(
                        workDataOf(
                            KEY_ERROR_MESSAGE to syncResult.reason,
                            KEY_ERROR_TYPE to "CANCELLED",
                            KEY_ITEMS_PROCESSED to syncResult.itemsProcessed
                        )
                    )
                }
            }
            
        } catch (e: Exception) {
            Result.failure(
                workDataOf(
                    KEY_ERROR_MESSAGE to (e.message ?: "Unknown error"),
                    KEY_ERROR_TYPE to e::class.simpleName
                )
            )
        }
    }
    
    companion object {
        // Input/Output data keys
        const val KEY_SYNC_TYPE = "sync_type"
        const val KEY_ITEMS_PROCESSED = "items_processed"
        const val KEY_ITEMS_ADDED = "items_added"
        const val KEY_ITEMS_UPDATED = "items_updated"
        const val KEY_ITEMS_DELETED = "items_deleted"
        const val KEY_DURATION_MS = "duration_ms"
        const val KEY_ERROR_MESSAGE = "error_message"
        const val KEY_ERROR_TYPE = "error_type"
        private const val TAG = "MediaSyncWorker"

        /**
         * Create work request for immediate sync
         */
        fun createImmediateSyncRequest(syncType: SyncType = SyncType.MANUAL): OneTimeWorkRequest {
            return OneTimeWorkRequestBuilder<MediaSyncWorker>()
                .setInputData(
                    workDataOf(KEY_SYNC_TYPE to syncType.name)
                )
                .setConstraints(createSyncConstraints())
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    SyncConstants.SYNC_BACKOFF_DELAY_MILLIS,
                    java.util.concurrent.TimeUnit.MILLISECONDS
                )
                .addTag(SyncConstants.IMMEDIATE_SYNC_WORK_NAME)
                .build()
        }
        
        /**
         * Create work request for periodic sync
         */
        fun createPeriodicSyncRequest(): PeriodicWorkRequest {
            return PeriodicWorkRequestBuilder<MediaSyncWorker>(
                SyncConstants.SYNC_INTERVAL_HOURS,
                java.util.concurrent.TimeUnit.HOURS,
                SyncConstants.SYNC_FLEX_INTERVAL_HOURS,
                java.util.concurrent.TimeUnit.HOURS
            )
                .setInputData(
                    workDataOf(KEY_SYNC_TYPE to SyncType.PERIODIC.name)
                )
                .setConstraints(createSyncConstraints())
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    SyncConstants.SYNC_BACKOFF_DELAY_MILLIS,
                    java.util.concurrent.TimeUnit.MILLISECONDS
                )
                .addTag(SyncConstants.PERIODIC_SYNC_WORK_NAME)
                .build()
        }
        
        /**
         * Create constraints for sync work
         */
        private fun createSyncConstraints(): Constraints {
            return Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(true)
                .setRequiresStorageNotLow(true)
                .build()
        }
    }
}
