package com.alpha.gallery.core.ui.permission

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.alpha.gallery.core.sync.permission.PermissionManager
import javax.inject.Inject

/**
 * Represents the current state of permissions
 */
data class PermissionState(
    val hasAllPermissions: Boolean = false,
    val missingPermissions: List<String> = emptyList(),
    val shouldShowRationale: Boolean = false,
    val isLoading: Boolean = true,
    val permissionDeniedPermanently: Boolean = false
)

/**
 * Composable that provides permission state management
 */
@Composable
fun rememberPermissionState(
    permissionManager: PermissionManager
): MutableState<PermissionState> {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    
    val permissionState = remember {
        mutableStateOf(PermissionState(isLoading = true))
    }
    
    // Update permission state when lifecycle resumes
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                updatePermissionState(permissionManager, permissionState, context)
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        // Initial check
        updatePermissionState(permissionManager, permissionState, context)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    return permissionState
}

/**
 * Update the permission state based on current permissions
 */
private fun updatePermissionState(
    permissionManager: PermissionManager,
    permissionState: MutableState<PermissionState>,
    context: android.content.Context
) {
    val hasAllPermissions = permissionManager.hasMediaPermissions()
    val missingPermissions = permissionManager.getMissingPermissions()
    
    // Check if we should show rationale (only if we have an activity context)
    val shouldShowRationale = if (context is android.app.Activity) {
        permissionManager.shouldShowRationale(context)
    } else false
    
    // If permissions are missing and we shouldn't show rationale,
    // it might mean permissions were denied permanently
    val permissionDeniedPermanently = missingPermissions.isNotEmpty() && !shouldShowRationale
    
    permissionState.value = PermissionState(
        hasAllPermissions = hasAllPermissions,
        missingPermissions = missingPermissions,
        shouldShowRationale = shouldShowRationale,
        isLoading = false,
        permissionDeniedPermanently = permissionDeniedPermanently
    )
}

/**
 * Permission callback interface
 */
interface PermissionCallback {
    fun onPermissionGranted()
    fun onPermissionDenied(permanentlyDenied: Boolean)
    fun onPermissionRationale()
}

/**
 * Default permission callback implementation
 */
class DefaultPermissionCallback(
    private val onGranted: () -> Unit = {},
    private val onDenied: (Boolean) -> Unit = { _ -> },
    private val onRationale: () -> Unit = {}
) : PermissionCallback {
    
    override fun onPermissionGranted() = onGranted()
    override fun onPermissionDenied(permanentlyDenied: Boolean) = onDenied(permanentlyDenied)
    override fun onPermissionRationale() = onRationale()
}
