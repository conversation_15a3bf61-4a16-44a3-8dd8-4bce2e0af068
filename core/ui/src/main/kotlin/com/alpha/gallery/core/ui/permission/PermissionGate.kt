package com.alpha.gallery.core.ui.permission

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.alpha.gallery.core.sync.permission.PermissionManager

/**
 * A gate component that checks permissions before showing content
 */
@Composable
fun PermissionGate(
    permissionManager: PermissionManager,
    onPermissionGranted: () -> Unit = {},
    showFullScreen: Boolean = false,
    allowSkip: Boolean = false,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    val permissionState = rememberPermissionState(permissionManager)
    var showPermissionDialog by remember { mutableStateOf(false) }
    var skipPermissions by remember { mutableStateOf(false) }
    
    // Permission launcher
    val requestPermissions = rememberPermissionLauncher(
        permissionManager = permissionManager,
        onPermissionResult = { granted, permanentlyDenied ->
            if (granted) {
                onPermissionGranted()
            } else {
                // Update permission state to reflect the result
                permissionState.value = permissionState.value.copy(
                    permissionDeniedPermanently = permanentlyDenied
                )
                if (!permanentlyDenied) {
                    showPermissionDialog = true
                }
            }
        }
    )
    
    // Handle permission state
    LaunchedEffect(permissionState.value) {
        if (!permissionState.value.isLoading && !permissionState.value.hasAllPermissions) {
            if (!showFullScreen) {
                showPermissionDialog = true
            }
        }
    }
    
    when {
        permissionState.value.isLoading -> {
            // Show loading state
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }

        permissionState.value.hasAllPermissions || skipPermissions -> {
            // Show content when permissions are granted or skipped
            content()
        }

        showFullScreen -> {
            // Show full-screen permission request
            PermissionScreen(
                permissionManager = permissionManager,
                onRequestPermissions = requestPermissions,
                onOpenSettings = {
                    context.startActivity(permissionManager.createAppSettingsIntent())
                },
                onSkip = if (allowSkip) {
                    { skipPermissions = true }
                } else null,
                showSettingsOption = permissionState.value.permissionDeniedPermanently
            )
        }
        
        else -> {
            // Show content with dialog overlay
            content()
            
            if (showPermissionDialog) {
                PermissionDialog(
                    permissionManager = permissionManager,
                    onDismiss = {
                        showPermissionDialog = false
                        if (allowSkip) {
                            skipPermissions = true
                        }
                    },
                    onRequestPermissions = {
                        showPermissionDialog = false
                        requestPermissions()
                    },
                    onOpenSettings = {
                        showPermissionDialog = false
                        context.startActivity(permissionManager.createAppSettingsIntent())
                    },
                    showSettingsOption = permissionState.value.permissionDeniedPermanently
                )
            }
        }
    }
}

/**
 * Simplified permission gate for quick checks
 */
@Composable
fun SimplePermissionGate(
    permissionManager: PermissionManager,
    onPermissionGranted: () -> Unit = {},
    fallbackContent: @Composable () -> Unit = { PermissionRequiredPlaceholder() },
    content: @Composable () -> Unit
) {
    val permissionState = rememberPermissionState(permissionManager)
    
    when {
        permissionState.value.isLoading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
        
        permissionState.value.hasAllPermissions -> {
            LaunchedEffect(Unit) {
                onPermissionGranted()
            }
            content()
        }
        
        else -> {
            fallbackContent()
        }
    }
}

/**
 * Default placeholder when permissions are not granted
 */
@Composable
fun PermissionRequiredPlaceholder() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Text(
                text = "Media Access Required",
                style = MaterialTheme.typography.headlineSmall
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Please grant media permissions to view your photos and videos.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
