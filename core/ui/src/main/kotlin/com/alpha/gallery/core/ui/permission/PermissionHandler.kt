package com.alpha.gallery.core.ui.permission

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.alpha.gallery.core.sync.permission.PermissionManager
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Composable that handles permission requests and provides callbacks
 */
@Composable
fun PermissionHandler(
    permissionManager: PermissionManager,
    permissionCallback: PermissionCallback,
    content: @Composable (requestPermissions: () -> Unit) -> Unit
) {
    val context = LocalContext.current
    val activity = context as? Activity
    
    // Permission launcher for requesting multiple permissions
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        handlePermissionResult(permissions, permissionManager, permissionCallback, activity)
    }
    
    // Function to request permissions
    val requestPermissions = {
        val missingPermissions = permissionManager.getMissingPermissions()
        if (missingPermissions.isNotEmpty()) {
            permissionLauncher.launch(missingPermissions.toTypedArray())
        }
    }
    
    content(requestPermissions)
}

/**
 * Handle the result of permission request
 */
private fun handlePermissionResult(
    permissions: Map<String, Boolean>,
    permissionManager: PermissionManager,
    permissionCallback: PermissionCallback,
    activity: Activity?
) {
    val allGranted = permissions.values.all { it }
    
    if (allGranted) {
        permissionCallback.onPermissionGranted()
    } else {
        // Check if any permission was permanently denied
        val permanentlyDenied = if (activity != null) {
            permissions.keys.any { permission ->
                !permissions[permission]!! && 
                !androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
            }
        } else false
        
        if (permanentlyDenied) {
            permissionCallback.onPermissionDenied(true)
        } else {
            permissionCallback.onPermissionRationale()
        }
    }
}

/**
 * Composable that provides a simple permission request function
 */
@Composable
fun rememberPermissionLauncher(
    permissionManager: PermissionManager,
    onPermissionResult: (granted: Boolean, permanentlyDenied: Boolean) -> Unit
): () -> Unit {
    val context = LocalContext.current
    val activity = context as? Activity
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        
        if (allGranted) {
            onPermissionResult(true, false)
        } else {
            val permanentlyDenied = if (activity != null) {
                permissions.keys.any { permission ->
                    !permissions[permission]!! && 
                    !androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
                }
            } else false
            
            onPermissionResult(false, permanentlyDenied)
        }
    }
    
    return remember {
        {
            val missingPermissions = permissionManager.getMissingPermissions()
            if (missingPermissions.isNotEmpty()) {
                permissionLauncher.launch(missingPermissions.toTypedArray())
            } else {
                onPermissionResult(true, false)
            }
        }
    }
}

/**
 * Get PermissionManager from Hilt in Compose
 */
@Composable
fun getPermissionManager(): PermissionManager {
    val context = LocalContext.current
    return remember {
        EntryPointAccessors.fromApplication(
            context.applicationContext,
            PermissionManagerEntryPoint::class.java
        ).permissionManager()
    }
}

/**
 * Hilt entry point for accessing PermissionManager in Compose
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface PermissionManagerEntryPoint {
    fun permissionManager(): PermissionManager
}
