package com.alpha.gallery.data.local.di

import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.data.local.repository.LocalMediaRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for local data dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class LocalDataModule {
    
    @Binds
    @Singleton
    abstract fun bindMediaRepository(
        localMediaRepository: LocalMediaRepository
    ): MediaRepository
}
