package com.alpha.gallery.data.local.repository

import com.alpha.gallery.core.database.dao.MediaDao
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.domain.model.Album
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.data.local.mapper.MediaMapper
import com.alpha.gallery.core.sync.manager.MediaSyncManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MediaRepository using local database
 */
@Singleton
class LocalMediaRepository @Inject constructor(
    private val mediaDao: MediaDao,
    private val albumDao: AlbumDao,
    private val mediaSyncManager: MediaSyncManager,
    private val mediaMapper: MediaMapper
) : MediaRepository {
    
    override fun getAllMediaItems(): Flow<List<MediaItem>> {
        return mediaDao.getAllMediaItems().map { entities ->
            entities.map { mediaMapper.mapToMediaItem(it) }
        }
    }
    
    override fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItem>> {
        return mediaDao.getMediaItemsByAlbum(albumId).map { entities ->
            entities.map { mediaMapper.mapToMediaItem(it) }
        }
    }
    
    override suspend fun getMediaItemById(id: String): MediaItem? {
        val entityId = id.toLongOrNull() ?: return null
        val entity = mediaDao.getMediaItemById(entityId)
        return entity?.let { mediaMapper.mapToMediaItem(it) }
    }
    
    override fun getAllAlbums(): Flow<List<Album>> {
        return albumDao.getAllAlbums().map { entities ->
            entities.map { mediaMapper.mapToAlbum(it) }
        }
    }
    
    override suspend fun getAlbumById(id: String): Album? {
        val entity = albumDao.getAlbumByBucketId(id)
        return entity?.let { mediaMapper.mapToAlbum(it) }
    }
    
    override fun searchMediaItems(query: String): Flow<List<MediaItem>> {
        return mediaDao.searchMediaItems(query).map { entities ->
            entities.map { mediaMapper.mapToMediaItem(it) }
        }
    }
    
    override fun getRecentMediaItems(limit: Int): Flow<List<MediaItem>> {
        return mediaDao.getRecentMediaItems(limit).map { entities ->
            entities.map { mediaMapper.mapToMediaItem(it) }
        }
    }
    
    override fun getFavoriteMediaItems(): Flow<List<MediaItem>> {
        return mediaDao.getFavoriteMediaItems().map { entities ->
            entities.map { mediaMapper.mapToMediaItem(it) }
        }
    }
    
    override suspend fun addToFavorites(mediaItemId: String) {
        val entityId = mediaItemId.toLongOrNull() ?: return
        mediaDao.markAsFavorite(entityId)
    }
    
    override suspend fun removeFromFavorites(mediaItemId: String) {
        val entityId = mediaItemId.toLongOrNull() ?: return
        mediaDao.removeFromFavorites(entityId)
    }
    
    override suspend fun deleteMediaItem(mediaItemId: String): Boolean {
        return try {
            val entityId = mediaItemId.toLongOrNull() ?: return false
            mediaDao.softDeleteMediaItem(entityId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    override suspend fun refreshMediaItems() {
        // Trigger immediate sync
        mediaSyncManager.performSmartSync()
    }
    
    override suspend fun syncWithCloud() {
        // This would be implemented when cloud sync is added
        // For now, just perform local sync
        mediaSyncManager.performSmartSync()
    }
}
