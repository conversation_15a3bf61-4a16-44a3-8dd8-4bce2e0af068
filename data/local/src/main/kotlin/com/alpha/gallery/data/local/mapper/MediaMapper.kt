package com.alpha.gallery.data.local.mapper

import com.alpha.gallery.core.database.entity.MediaEntity
import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.model.Album
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Maps between database entities and domain models
 */
@Singleton
class MediaMapper @Inject constructor() {
    
    /**
     * Convert MediaEntity to MediaItem domain model
     */
    fun mapToMediaItem(entity: MediaEntity): MediaItem {
        return MediaItem(
            id = entity.id.toString(),
            name = entity.displayName,
            path = entity.filePath,
            uri = entity.uri,
            mimeType = entity.mimeType,
            size = entity.size,
            dateAdded = entity.dateAdded,
            dateModified = entity.dateModified,
            width = entity.width,
            height = entity.height,
            duration = entity.duration,
            albumId = entity.albumId,
            albumName = entity.albumName,
            isVideo = entity.isVideo,
            thumbnailPath = entity.thumbnailPath,
            isCloudItem = false, // Local items are not cloud items
            cloudUrl = null,
            isSynced = entity.isSynced,
            isFavorite = entity.isFavorite
        )
    }
    
    /**
     * Convert AlbumEntity to Album domain model
     */
    fun mapToAlbum(entity: AlbumEntity): Album {
        return Album(
            id = entity.bucketId,
            name = entity.name,
            path = entity.path,
            coverImagePath = entity.coverImagePath,
            mediaCount = entity.mediaCount,
            dateAdded = entity.dateAdded,
            dateModified = entity.dateModified,
            isCloudAlbum = entity.isCloudAlbum
        )
    }
    
    /**
     * Convert list of MediaEntities to MediaItems
     */
    fun mapToMediaItems(entities: List<MediaEntity>): List<MediaItem> {
        return entities.map { mapToMediaItem(it) }
    }
    
    /**
     * Convert list of AlbumEntities to Albums
     */
    fun mapToAlbums(entities: List<AlbumEntity>): List<Album> {
        return entities.map { mapToAlbum(it) }
    }
}
