package com.alpha.gallery.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.permission.PermissionCoordinator
import com.alpha.gallery.permission.PermissionCoordinatorState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Main ViewModel for the Gallery app
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val permissionManager: PermissionManager,
    private val permissionCoordinator: PermissionCoordinator
) : ViewModel() {
    
    // Permission state from coordinator
    val permissionState: StateFlow<PermissionCoordinatorState> = 
        permissionCoordinator.permissionState
    
    // UI state
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // Combined state for UI
    val combinedState: StateFlow<CombinedAppState> = combine(
        permissionState,
        uiState
    ) { permissionState, uiState ->
        CombinedAppState(
            permissionState = permissionState,
            uiState = uiState,
            shouldShowPermissionSetup = !permissionState.hasPermissions && uiState.isFirstLaunch
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = CombinedAppState()
    )
    
    init {
        // Initialize app state
        initializeApp()
    }
    
    /**
     * Initialize the app
     */
    private fun initializeApp() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // Check if this is first launch
                val isFirstLaunch = checkIfFirstLaunch()
                
                // Trigger initial permission check
                permissionCoordinator.triggerPermissionCheckAndSync()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isFirstLaunch = isFirstLaunch,
                    isInitialized = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * Check if this is the first app launch
     */
    private suspend fun checkIfFirstLaunch(): Boolean {
        // TODO: Implement actual first launch detection using SharedPreferences or DataStore
        // For now, return true if no permissions are granted
        return !permissionManager.hasMediaPermissions()
    }
    
    /**
     * Handle permission granted event
     */
    fun onPermissionGranted() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isFirstLaunch = false,
                    permissionSetupCompleted = true
                )
                
                // Trigger sync after permission grant
                permissionCoordinator.forceSyncIfPermissionsAvailable()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message
                )
            }
        }
    }
    
    /**
     * Handle permission denied event
     */
    fun onPermissionDenied(permanentlyDenied: Boolean) {
        _uiState.value = _uiState.value.copy(
            permissionDeniedPermanently = permanentlyDenied
        )
    }
    
    /**
     * Clear error state
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * Retry initialization
     */
    fun retryInitialization() {
        initializeApp()
    }
    
    /**
     * Force permission check and sync
     */
    fun forcePermissionCheck() {
        viewModelScope.launch {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        permissionCoordinator.cleanup()
    }
}

/**
 * UI state for the main app
 */
data class MainUiState(
    val isLoading: Boolean = true,
    val isInitialized: Boolean = false,
    val isFirstLaunch: Boolean = true,
    val permissionSetupCompleted: Boolean = false,
    val permissionDeniedPermanently: Boolean = false,
    val error: String? = null
)

/**
 * Combined state for the entire app
 */
data class CombinedAppState(
    val permissionState: PermissionCoordinatorState = PermissionCoordinatorState(),
    val uiState: MainUiState = MainUiState(),
    val shouldShowPermissionSetup: Boolean = false
) {
    val isReady: Boolean
        get() = uiState.isInitialized && !uiState.isLoading
    
    val hasPermissions: Boolean
        get() = permissionState.hasPermissions
    
    val isSyncInProgress: Boolean
        get() = permissionState.isSyncInProgress
    
    val hasError: Boolean
        get() = uiState.error != null || permissionState.lastError != null
    
    val errorMessage: String?
        get() = uiState.error ?: permissionState.lastError
}
