package com.alpha.gallery

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.ui.theme.GalleryTheme
import com.alpha.gallery.navigation.GalleryNavigation
import com.alpha.gallery.navigation.GalleryDestinations
import com.alpha.gallery.permission.PermissionCoordinator
import com.alpha.gallery.ui.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var permissionCoordinator: PermissionCoordinator

    @Inject
    lateinit var permissionManager: PermissionManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            GalleryTheme {
                GalleryApp(
                    permissionCoordinator = permissionCoordinator,
                    permissionManager = permissionManager
                )
            }
        }
    }
}

/**
 * Main app composable
 */
@Composable
fun GalleryApp(
    permissionCoordinator: PermissionCoordinator,
    permissionManager: PermissionManager,
    viewModel: MainViewModel = hiltViewModel()
) {
    val appState by viewModel.combinedState.collectAsStateWithLifecycle()

    when {
        !appState.isReady -> {
            // Show loading screen
            LoadingScreen()
        }

        appState.hasError -> {
            // Show error screen
            ErrorScreen(
                error = appState.errorMessage ?: "Unknown error",
                onRetry = { viewModel.retryInitialization() },
                onDismiss = { viewModel.clearError() }
            )
        }

        appState.shouldShowPermissionSetup -> {
            // Show permission setup as starting point
            GalleryNavigation(
                permissionCoordinator = permissionCoordinator,
                permissionManager = permissionManager,
                startDestination = GalleryDestinations.PERMISSION_SETUP
            )
        }

        else -> {
            // Show main app navigation
            GalleryNavigation(
                permissionCoordinator = permissionCoordinator,
                permissionManager = permissionManager,
                startDestination = GalleryDestinations.GALLERY
            )
        }
    }
}

/**
 * Loading screen
 */
@Composable
private fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Initializing Gallery...",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * Error screen
 */
@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Error",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Dismiss")
                    }

                    Button(onClick = onRetry) {
                        Text("Retry")
                    }
                }
            }
        }
    }
}
