package com.alpha.gallery.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.ui.permission.*
import com.alpha.gallery.permission.PermissionCoordinator
import com.alpha.gallery.feature.gallery.GalleryScreen
import com.alpha.gallery.feature.mediaviewer.MediaViewerScreen

/**
 * Main navigation destinations
 */
object GalleryDestinations {
    const val PERMISSION_SETUP = "permission_setup"
    const val GALLERY = "gallery"
    const val ALBUMS = "albums"
    const val MEDIA_VIEWER = "media_viewer"
    const val CLOUD = "cloud"
    const val SETTINGS = "settings"
}

/**
 * Main navigation component for the Gallery app
 */
@Composable
fun GalleryNavigation(
    navController: NavHostController = rememberNavController(),
    permissionCoordinator: PermissionCoordinator,
    permissionManager: PermissionManager,
    startDestination: String = GalleryDestinations.GALLERY
) {
    val context = LocalContext.current
    
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Permission setup screen
        composable(GalleryDestinations.PERMISSION_SETUP) {
            PermissionSetupScreen(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator,
                onPermissionsGranted = {
                    navController.navigate(GalleryDestinations.GALLERY) {
                        popUpTo(GalleryDestinations.PERMISSION_SETUP) { inclusive = true }
                    }
                },
                onSkip = {
                    navController.navigate(GalleryDestinations.GALLERY) {
                        popUpTo(GalleryDestinations.PERMISSION_SETUP) { inclusive = true }
                    }
                }
            )
        }
        
        // Main gallery screen
        composable(GalleryDestinations.GALLERY) {
            GalleryScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator,
                onNavigateToPermissionSetup = {
                    navController.navigate(GalleryDestinations.PERMISSION_SETUP)
                }
            )
        }
        
        // Albums screen
        composable(GalleryDestinations.ALBUMS) {
            AlbumsScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
        
        // Media viewer screen
        composable(
            route = "${GalleryDestinations.MEDIA_VIEWER}/{mediaItemId}",
            arguments = listOf(
                navArgument("mediaItemId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val mediaItemId = backStackEntry.arguments?.getString("mediaItemId") ?: ""
            MediaViewerScreenWithPermissionGate(
                mediaItemId = mediaItemId,
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
        
        // Cloud screen
        composable(GalleryDestinations.CLOUD) {
            CloudScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
        
        // Settings screen
        composable(GalleryDestinations.SETTINGS) {
            SettingsScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
    }
}

/**
 * Permission setup screen wrapper
 */
@Composable
private fun PermissionSetupScreen(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator,
    onPermissionsGranted: () -> Unit,
    onSkip: () -> Unit
) {
    val context = LocalContext.current
    var permissionGranted by remember { mutableStateOf(false) }

    // Trigger sync when permission is granted
    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
            onPermissionsGranted()
        }
    }

    val requestPermissions = rememberPermissionLauncher(
        permissionManager = permissionManager,
        onPermissionResult = { granted, permanentlyDenied ->
            if (granted) {
                permissionGranted = true
            }
        }
    )
    
    PermissionScreen(
        permissionManager = permissionManager,
        onRequestPermissions = requestPermissions,
        onOpenSettings = {
            context.startActivity(permissionManager.createAppSettingsIntent())
        },
        onSkip = onSkip,
        showSettingsOption = false
    )
}

/**
 * Gallery screen with permission gate
 */
@Composable
private fun GalleryScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator,
    onNavigateToPermissionSetup: () -> Unit
) {
    var permissionGranted by remember { mutableStateOf(false) }

    // Trigger sync when permissions are granted
    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    PermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        },
        showFullScreen = false,
        allowSkip = true
    ) {
        GalleryScreen(
            onNavigateToMediaViewer = { itemId ->
                navController.navigate("${GalleryDestinations.MEDIA_VIEWER}/$itemId")
            },
            onShowMessage = { message ->
                // TODO: Show snackbar or toast
            },
            onShowError = { error ->
                // TODO: Show error snackbar or toast
            }
        )
    }
}

/**
 * Albums screen with permission gate
 */
@Composable
private fun AlbumsScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    var permissionGranted by remember { mutableStateOf(false) }

    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    SimplePermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        }
    ) {
        // TODO: Replace with actual albums screen
        AlbumsPlaceholderScreen()
    }
}

/**
 * Media viewer screen with permission gate
 */
@Composable
private fun MediaViewerScreenWithPermissionGate(
    mediaItemId: String,
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator,
    onNavigateBack: () -> Unit
) {
    var permissionGranted by remember { mutableStateOf(false) }

    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    SimplePermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        }
    ) {
        MediaViewerScreen(
            mediaItemId = mediaItemId,
            onNavigateBack = onNavigateBack,
            onShowMessage = { message ->
                // TODO: Show snackbar or toast
            },
            onShowError = { error ->
                // TODO: Show error snackbar or toast
            }
        )
    }
}

/**
 * Cloud screen with permission gate
 */
@Composable
private fun CloudScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    var permissionGranted by remember { mutableStateOf(false) }

    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    SimplePermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        }
    ) {
        // TODO: Replace with actual cloud screen
        CloudPlaceholderScreen()
    }
}

/**
 * Settings screen with permission gate
 */
@Composable
private fun SettingsScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    // Settings screen might not need strict permission enforcement
    SettingsPlaceholderScreen()
}

// Placeholder screens - TODO: Replace with actual feature implementations

@Composable
private fun GalleryPlaceholderScreen() {
    PlaceholderScreen(
        title = "Gallery",
        description = "Your photos and videos will appear here once media sync is complete."
    )
}

@Composable
private fun AlbumsPlaceholderScreen() {
    PlaceholderScreen(
        title = "Albums",
        description = "Your photo albums will be organized and displayed here."
    )
}

@Composable
private fun MediaViewerPlaceholderScreen() {
    PlaceholderScreen(
        title = "Media Viewer",
        description = "View and interact with your media files here."
    )
}

@Composable
private fun CloudPlaceholderScreen() {
    PlaceholderScreen(
        title = "Cloud Storage",
        description = "Sync and manage your cloud media files here."
    )
}

@Composable
private fun SettingsPlaceholderScreen() {
    PlaceholderScreen(
        title = "Settings",
        description = "Configure your gallery preferences and sync settings."
    )
}

@Composable
private fun PlaceholderScreen(
    title: String,
    description: String
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
