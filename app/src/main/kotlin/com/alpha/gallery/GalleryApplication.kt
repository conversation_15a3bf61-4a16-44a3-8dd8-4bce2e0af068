package com.alpha.gallery

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.alpha.gallery.core.sync.initializer.SyncInitializer
import com.alpha.gallery.core.sync.observer.MediaStoreObserver
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import com.alpha.gallery.permission.PermissionCoordinator
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * Application class for Gallery app with Hilt and WorkManager setup
 */
@HiltAndroidApp
class GalleryApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var syncWorkScheduler: SyncWorkScheduler

    @Inject
    lateinit var mediaStoreObserver: MediaStoreObserver

    @Inject
    lateinit var syncInitializer: SyncInitializer

    @Inject
    lateinit var permissionCoordinator: PermissionCoordinator

    override fun onCreate() {
        super.onCreate()

        // Initialize sync components with permission awareness
        initializeSync()
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()

    /**
     * Initialize sync components with permission awareness
     */
    private fun initializeSync() {
        // Initialize sync system (will check permissions internally)
        syncInitializer.initialize()

        // Permission coordinator will handle permission changes and trigger sync
        // No need to manually start observers here as they will be started when permissions are granted
    }

    override fun onTerminate() {
        super.onTerminate()

        // Clean up resources
        mediaStoreObserver.cleanup()
        syncInitializer.cleanup()
        permissionCoordinator.cleanup()
    }
}
