package com.alpha.gallery.permission

import android.content.Context
import com.alpha.gallery.core.sync.initializer.SyncInitializer
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.sync.scheduler.SyncWorkScheduler
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Coordinates permission management with media synchronization
 */
@Singleton
class PermissionCoordinator @Inject constructor(
    @ApplicationContext private val context: Context,
    private val permissionManager: PermissionManager,
    private val syncInitializer: SyncInitializer,
    private val syncWorkScheduler: SyncWorkScheduler
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _permissionState = MutableStateFlow(PermissionCoordinatorState())
    val permissionState: StateFlow<PermissionCoordinatorState> = _permissionState.asStateFlow()
    
    private var lastPermissionCheck = 0L
    private val permissionCheckInterval = 1000L // 1 second
    
    init {
        // Start monitoring permission state
        startPermissionMonitoring()
    }
    
    /**
     * Start monitoring permission changes
     */
    private fun startPermissionMonitoring() {
        scope.launch {
            while (true) {
                checkAndUpdatePermissionState()
                delay(permissionCheckInterval)
            }
        }
    }
    
    /**
     * Check current permission state and update if changed
     */
    private suspend fun checkAndUpdatePermissionState() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastPermissionCheck < permissionCheckInterval) {
            return
        }
        
        lastPermissionCheck = currentTime
        
        val hasPermissions = permissionManager.hasMediaPermissions()
        val currentState = _permissionState.value
        
        if (hasPermissions != currentState.hasPermissions) {
            _permissionState.value = currentState.copy(
                hasPermissions = hasPermissions,
                lastChecked = currentTime
            )
            
            // Handle permission change
            handlePermissionChange(hasPermissions)
        }
    }
    
    /**
     * Handle permission state changes
     */
    private suspend fun handlePermissionChange(hasPermissions: Boolean) {
        if (hasPermissions) {
            onPermissionsGranted()
        } else {
            onPermissionsRevoked()
        }
    }
    
    /**
     * Called when permissions are granted
     */
    private suspend fun onPermissionsGranted() {
        try {
            _permissionState.value = _permissionState.value.copy(
                isSyncInProgress = true,
                lastSyncTrigger = System.currentTimeMillis()
            )
            
            // Notify sync initializer about permission change
            syncInitializer.onPermissionsChanged()
            
            // Trigger immediate full sync
            syncWorkScheduler.scheduleImmediateSync()
            
            _permissionState.value = _permissionState.value.copy(
                isSyncInProgress = false,
                lastSuccessfulSync = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            _permissionState.value = _permissionState.value.copy(
                isSyncInProgress = false,
                lastError = e.message
            )
        }
    }
    
    /**
     * Called when permissions are revoked
     */
    private suspend fun onPermissionsRevoked() {
        try {
            // Notify sync initializer about permission change
            syncInitializer.onPermissionsChanged()
            
            _permissionState.value = _permissionState.value.copy(
                lastError = "Media permissions revoked"
            )
            
        } catch (e: Exception) {
            _permissionState.value = _permissionState.value.copy(
                lastError = e.message
            )
        }
    }
    
    /**
     * Manually trigger permission check and sync
     */
    suspend fun triggerPermissionCheckAndSync() {
        checkAndUpdatePermissionState()
        
        if (permissionManager.hasMediaPermissions()) {
            onPermissionsGranted()
        }
    }
    
    /**
     * Force immediate sync if permissions are available
     */
    suspend fun forceSyncIfPermissionsAvailable() {
        if (permissionManager.hasMediaPermissions()) {
            onPermissionsGranted()
        }
    }
    
    /**
     * Get current permission status
     */
    fun getCurrentPermissionStatus() = permissionManager.getPermissionStatus()
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        scope.cancel()
    }
}

/**
 * State of the permission coordinator
 */
data class PermissionCoordinatorState(
    val hasPermissions: Boolean = false,
    val isSyncInProgress: Boolean = false,
    val lastChecked: Long = 0L,
    val lastSyncTrigger: Long = 0L,
    val lastSuccessfulSync: Long = 0L,
    val lastError: String? = null
)

/**
 * Extension function to check if sync was recently triggered
 */
fun PermissionCoordinatorState.wasSyncRecentlyTriggered(withinMs: Long = 30000L): Boolean {
    return lastSyncTrigger > 0 && (System.currentTimeMillis() - lastSyncTrigger) < withinMs
}

/**
 * Extension function to check if there's a recent error
 */
fun PermissionCoordinatorState.hasRecentError(withinMs: Long = 60000L): Boolean {
    return lastError != null && lastChecked > 0 && (System.currentTimeMillis() - lastChecked) < withinMs
}
